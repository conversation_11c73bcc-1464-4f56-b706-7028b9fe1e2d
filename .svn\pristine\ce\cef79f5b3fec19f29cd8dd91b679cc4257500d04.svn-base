using BLL;
using Model;
using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace YdGSP
{
    public partial class Yp_Bhg2 : Common.BaseForm.BaseDict2
    {
        BLL.BllYp_Bhg _bllYp_Bhg = new BllYp_Bhg();
        Model.MdlYp_Bhg _mdlYp_Bhg = new MdlYp_Bhg();

        public Yp_Bhg2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            TxtYpPzwh.GotFocus += new System.EventHandler(base.InputCn);
            TxtYpScph.GotFocus += new System.EventHandler(base.InputCn);
            TxtYpJx.GotFocus += new System.EventHandler(base.InputCn);
            TxtYpZjgg.GotFocus += new System.EventHandler(base.InputCn);
            TxtYpBzgg.GotFocus += new System.EventHandler(base.InputCn);
            TxtYpScqy.GotFocus += new System.EventHandler(base.InputCn);
            TxtBhgMemo.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void Yp_Bhg2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            TxtBhgCode.Enabled = false;
            TxtYpPzwh.Enabled = false;
            TxtYpScph.Enabled = false;
            TxtYpJx.Enabled = false;
            TxtYpZjgg.Enabled = false;
            TxtYpBzgg.Enabled = false;
            TxtYpScqy.Enabled = false;
            comboYpPh1.Init();

            myGrid1.Init_Grid();
            myGrid1.Init_Column("追溯码", "Sy_Code", 150, "中", "", false);
            myGrid1.AllowSort = true;

            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);
        }

        private void DataClear()
        {
            TxtBhgCode.Text = "";
            DtpBhgDate.Value = DateTime.Now;
            comboYpPh1.SelectedIndex = -1;
            TxtYpPzwh.Text = "";
            TxtYpScph.Text = "";
            TxtYpJx.Text = "";
            TxtYpZjgg.Text = "";
            TxtYpBzgg.Text = "";
            TxtYpScqy.Text = "";
            TxtBhgMemo.Text = "";
        }

        private void DataShow(DataRow row)
        {
            TxtBhgCode.Text = row["Bhg_Code"].ToString();
            comboYpPh1.SelectedValue = row["Yp_Code"].ToString();
            if (row["Bhg_Date"] != DBNull.Value)
                DtpBhgDate.Value = Convert.ToDateTime(row["Bhg_Date"]);
            TxtYpPzwh.Text = row["Yp_Pzwh"].ToString();
            TxtYpScph.Text = row["Yp_Scph"].ToString();
            TxtYpJx.Text = row["Yp_Jx"].ToString();
            TxtYpZjgg.Text = row["Yp_Zjgg"].ToString();
            TxtYpBzgg.Text = row["Yp_Bzgg"].ToString();
            TxtYpScqy.Text = row["Yp_Scqy"].ToString();
            TxtBhgMemo.Text = row["Bhg_Memo"].ToString();
        }

        private bool DataCheck()
        {
            if (CustomControl.Func.NotAllowEmpty(comboYpPh1)) return false;
            return true;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlYp_Bhg.Bhg_Code = _bllYp_Bhg.MaxCode(9);
            TxtBhgCode.Text = _mdlYp_Bhg.Bhg_Code;
            _mdlYp_Bhg.Yp_Code = comboYpPh1.SelectedValue.ToString();
            _mdlYp_Bhg.Bhg_Date = DtpBhgDate.Value as DateTime?;
            _mdlYp_Bhg.Yp_Pzwh = TxtYpPzwh.Text.Trim();
            _mdlYp_Bhg.Yp_Scph = TxtYpScph.Text.Trim();
            _mdlYp_Bhg.Yp_Name = comboYpPh1.Text.Trim();
            _mdlYp_Bhg.Yp_Jc = comboYpPh1.Columns["Yp_Jc"].Value.ToString();
            _mdlYp_Bhg.Yp_Jx = TxtYpJx.Text.Trim();
            _mdlYp_Bhg.Yp_Zjgg = TxtYpZjgg.Text.Trim();
            _mdlYp_Bhg.Yp_Bzgg = TxtYpBzgg.Text.Trim();
            _mdlYp_Bhg.Yp_Scqy = TxtYpScqy.Text.Trim();
            _mdlYp_Bhg.Bhg_Memo = TxtBhgMemo.Text.Trim();

            Common.DataTableToList.ToDataRow(_mdlYp_Bhg, base.MyRow);

            //数据保存
            try
            {
                _bllYp_Bhg.Add(_mdlYp_Bhg);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show("保存失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            MessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            this.Close();
        }

        private void DataEdit()
        {
            _mdlYp_Bhg = _bllYp_Bhg.GetModel(base.MyRow["Bhg_Code"].ToString());
            _mdlYp_Bhg.Bhg_Code = TxtBhgCode.Text.Trim();
            _mdlYp_Bhg.Yp_Code = comboYpPh1.SelectedValue.ToString();
            _mdlYp_Bhg.Bhg_Date = DtpBhgDate.Value as DateTime?;
            _mdlYp_Bhg.Yp_Pzwh = TxtYpPzwh.Text.Trim();
            _mdlYp_Bhg.Yp_Scph = TxtYpScph.Text.Trim();
            _mdlYp_Bhg.Yp_Name = comboYpPh1.Text.Trim();
            _mdlYp_Bhg.Yp_Jc = comboYpPh1.Columns["Yp_Jc"].Value.ToString();
            _mdlYp_Bhg.Yp_Jx = TxtYpJx.Text.Trim();
            _mdlYp_Bhg.Yp_Zjgg = TxtYpZjgg.Text.Trim();
            _mdlYp_Bhg.Yp_Bzgg = TxtYpBzgg.Text.Trim();
            _mdlYp_Bhg.Yp_Scqy = TxtYpScqy.Text.Trim();
            _mdlYp_Bhg.Bhg_Memo = TxtBhgMemo.Text.Trim();

            Common.DataTableToList.ToDataRow(_mdlYp_Bhg, base.MyRow);

            //数据保存
            try
            {
                _bllYp_Bhg.Update(_mdlYp_Bhg);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show("保存失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            MessageBox.Show("保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            this.Close();
        }

        #endregion

        #region 控件动作

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck() == false)
                return;

            if (base.Insert == true)
                this.DataAdd();
            else
                this.DataEdit();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        private void comboYpPh1_RowChange(object sender, EventArgs e)
        {
            if (comboYpPh1.WillChangeToValue == null)
            {
                TxtYpJx.Text = "";
                TxtYpZjgg.Text = "";
                TxtYpBzgg.Text = "";
                TxtYpScqy.Text = "";
            }
            else
            {
                TxtYpJx.Text = comboYpPh1.Columns["Yp_Jx"].Value + "";
                TxtYpZjgg.Text = comboYpPh1.Columns["Yp_Zjgg"].Value + "";
                TxtYpBzgg.Text = comboYpPh1.Columns["Yp_Bzgg"].Value + "";
                TxtYpScqy.Text = comboYpPh1.Columns["Yp_Scqy"].Value + "";
            }
        }

        #endregion


    }
}
