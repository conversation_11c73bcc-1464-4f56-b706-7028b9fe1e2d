﻿/**  版本信息模板在安装目录下，可自行修改。
* DalZd_Yp4.cs
*
* 功 能： N/A
* 类 名： DalZd_Yp4
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:28   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalZd_Yp4
	/// </summary>
	public partial class DalZd_Yp4 : IDalZd_Yp4
	{
		public DalZd_Yp4()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Sy_Code)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from Zd_Yp4");
			strSql.Append(" where Sy_Code=@Sy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Sy_Code", SqlDbType.Char,20)         };
			parameters[0].Value = Sy_Code;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlZd_Yp4 model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into Zd_Yp4(");
			strSql.Append("Yp_Code,Ph_Code,Tm_Code,Sy_Code,Rk_Code,Rk_Date,Ck_Code,Ck_Date,Yp_Xsdj,Yp_Exist,Rk_Sc_Finish,Ck_Sc_Finish,Kh_Code,Kh_Name)");
			strSql.Append(" values (");
			strSql.Append("@Yp_Code,@Ph_Code,@Tm_Code,@Sy_Code,@Rk_Code,@Rk_Date,@Ck_Code,@Ck_Date,@Yp_Xsdj,@Yp_Exist,@Rk_Sc_Finish,@Ck_Sc_Finish,@Kh_Code,@Kh_Name)");
			SqlParameter[] parameters = {
					new SqlParameter("@Yp_Code", SqlDbType.Char,11),
					new SqlParameter("@Ph_Code", SqlDbType.Char,3),
					new SqlParameter("@Tm_Code", SqlDbType.Char,13),
					new SqlParameter("@Sy_Code", SqlDbType.Char,20),
					new SqlParameter("@Rk_Code", SqlDbType.Char,9),
					new SqlParameter("@Rk_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Ck_Code", SqlDbType.Char,9),
					new SqlParameter("@Ck_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Yp_Xsdj", SqlDbType.Decimal,9),
					new SqlParameter("@Yp_Exist", SqlDbType.Bit,1),
					new SqlParameter("@Rk_Sc_Finish", SqlDbType.Bit,1),
					new SqlParameter("@Ck_Sc_Finish", SqlDbType.Bit,1),
					new SqlParameter("@Kh_Code", SqlDbType.Char,4),
					new SqlParameter("@Kh_Name", SqlDbType.VarChar,100)};
			parameters[0].Value = model.Yp_Code;
			parameters[1].Value = Common.Tools.IsValueNull(model.Ph_Code);
			parameters[2].Value = Common.Tools.IsValueNull(model.Tm_Code);
			parameters[3].Value = Common.Tools.IsValueNull(model.Sy_Code);
			parameters[4].Value = Common.Tools.IsValueNull(model.Rk_Code);
			parameters[5].Value = Common.Tools.IsValueNull(model.Rk_Date);
			parameters[6].Value = Common.Tools.IsValueNull(model.Ck_Code);
			parameters[7].Value = Common.Tools.IsValueNull(model.Ck_Date);
			parameters[8].Value = model.Yp_Xsdj;
			parameters[9].Value = model.Yp_Exist;
			parameters[10].Value = Common.Tools.IsValueNull(model.Rk_Sc_Finish);
			parameters[11].Value = Common.Tools.IsValueNull(model.Ck_Sc_Finish);
			parameters[12].Value = Common.Tools.IsValueNull(model.Kh_Code);
			parameters[13].Value = Common.Tools.IsValueNull(model.Kh_Name);

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlZd_Yp4 model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Zd_Yp4 set ");
			strSql.Append("Yp_Code=@Yp_Code,");
			strSql.Append("Ph_Code=@Ph_Code,");
			strSql.Append("Tm_Code=@Tm_Code,");
			strSql.Append("Rk_Code=@Rk_Code,");
			strSql.Append("Rk_Date=@Rk_Date,");
			strSql.Append("Ck_Code=@Ck_Code,");
			strSql.Append("Ck_Date=@Ck_Date,");
			strSql.Append("Yp_Xsdj=@Yp_Xsdj,");
			strSql.Append("Yp_Exist=@Yp_Exist,");
			strSql.Append("Rk_Sc_Finish=@Rk_Sc_Finish,");
			strSql.Append("Ck_Sc_Finish=@Ck_Sc_Finish,");
			strSql.Append("Kh_Code=@Kh_Code,");
			strSql.Append("Kh_Name=@Kh_Name");
			strSql.Append(" where Sy_Code=@Sy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yp_Code", SqlDbType.Char,11),
					new SqlParameter("@Ph_Code", SqlDbType.Char,3),
					new SqlParameter("@Tm_Code", SqlDbType.Char,13),
					new SqlParameter("@Rk_Code", SqlDbType.Char,9),
					new SqlParameter("@Rk_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Ck_Code", SqlDbType.Char,9),
					new SqlParameter("@Ck_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Yp_Xsdj", SqlDbType.Decimal,9),
					new SqlParameter("@Yp_Exist", SqlDbType.Bit,1),
					new SqlParameter("@Rk_Sc_Finish", SqlDbType.Bit,1),
					new SqlParameter("@Ck_Sc_Finish", SqlDbType.Bit,1),
					new SqlParameter("@Kh_Code", SqlDbType.Char,4),
					new SqlParameter("@Kh_Name", SqlDbType.VarChar,100),
					new SqlParameter("@Sy_Code", SqlDbType.Char,20)};
			parameters[0].Value = model.Yp_Code;
			parameters[1].Value = Common.Tools.IsValueNull(model.Ph_Code);
			parameters[2].Value = Common.Tools.IsValueNull(model.Tm_Code);
			parameters[3].Value = Common.Tools.IsValueNull(model.Rk_Code);
			parameters[4].Value = Common.Tools.IsValueNull(model.Rk_Date);
			parameters[5].Value = model.Ck_Code;
			parameters[6].Value = model.Ck_Date;
			parameters[7].Value = model.Yp_Xsdj;
			parameters[8].Value = model.Yp_Exist;
			parameters[9].Value = Common.Tools.IsValueNull(model.Rk_Sc_Finish);
			parameters[10].Value = Common.Tools.IsValueNull(model.Ck_Sc_Finish);
			parameters[11].Value = Common.Tools.IsValueNull(model.Kh_Code);
			parameters[12].Value = Common.Tools.IsValueNull(model.Kh_Name);
			parameters[13].Value = Common.Tools.IsValueNull(model.Sy_Code);

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Sy_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_Yp4 ");
			strSql.Append(" where Sy_Code=@Sy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Sy_Code", SqlDbType.Char,20)         };
			parameters[0].Value = Sy_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Sy_Codelist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_Yp4 ");
			strSql.Append(" where Sy_Code in (" + Sy_Codelist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_Yp4 GetModel(string Sy_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Yp_Code,Ph_Code,Tm_Code,Sy_Code,Rk_Code,Rk_Date,Ck_Code,Ck_Date,Yp_Xsdj,Yp_Exist,Rk_Sc_Finish,Ck_Sc_Finish,Kh_Code,Kh_Name from Zd_Yp4 ");
			strSql.Append(" where Sy_Code=@Sy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Sy_Code", SqlDbType.Char,20)         };
			parameters[0].Value = Sy_Code;

			Model.MdlZd_Yp4 model = new Model.MdlZd_Yp4();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_Yp4 DataRowToModel(DataRow row)
		{
			Model.MdlZd_Yp4 model = new Model.MdlZd_Yp4();
			if (row != null)
			{
				if (row["Yp_Code"] != null)
				{
					model.Yp_Code = row["Yp_Code"].ToString();
				}
				if (row["Ph_Code"] != null)
				{
					model.Ph_Code = row["Ph_Code"].ToString();
				}
				if (row["Tm_Code"] != null)
				{
					model.Tm_Code = row["Tm_Code"].ToString();
				}
				if (row["Sy_Code"] != null)
				{
					model.Sy_Code = row["Sy_Code"].ToString();
				}
				if (row["Rk_Code"] != null)
				{
					model.Rk_Code = row["Rk_Code"].ToString();
				}
				if (row["Rk_Date"] != null && row["Rk_Date"].ToString() != "")
				{
					model.Rk_Date = DateTime.Parse(row["Rk_Date"].ToString());
				}
				if (row["Ck_Code"] != null)
				{
					model.Ck_Code = row["Ck_Code"].ToString();
				}
				if (row["Ck_Date"] != null && row["Ck_Date"].ToString() != "")
				{
					model.Ck_Date = DateTime.Parse(row["Ck_Date"].ToString());
				}
				if (row["Yp_Xsdj"] != null && row["Yp_Xsdj"].ToString() != "")
				{
					model.Yp_Xsdj = decimal.Parse(row["Yp_Xsdj"].ToString());
				}
				if (row["Yp_Exist"] != null && row["Yp_Exist"].ToString() != "")
				{
					if ((row["Yp_Exist"].ToString() == "1") || (row["Yp_Exist"].ToString().ToLower() == "true"))
					{
						model.Yp_Exist = true;
					}
					else
					{
						model.Yp_Exist = false;
					}
				}
				if (row["Rk_Sc_Finish"] != null && row["Rk_Sc_Finish"].ToString() != "")
				{
					if ((row["Rk_Sc_Finish"].ToString() == "1") || (row["Rk_Sc_Finish"].ToString().ToLower() == "true"))
					{
						model.Rk_Sc_Finish = true;
					}
					else
					{
						model.Rk_Sc_Finish = false;
					}
				}
				if (row["Ck_Sc_Finish"] != null && row["Ck_Sc_Finish"].ToString() != "")
				{
					if ((row["Ck_Sc_Finish"].ToString() == "1") || (row["Ck_Sc_Finish"].ToString().ToLower() == "true"))
					{
						model.Ck_Sc_Finish = true;
					}
					else
					{
						model.Ck_Sc_Finish = false;
					}
				}
				if (row["Kh_Code"] != null)
				{
					model.Kh_Code = row["Kh_Code"].ToString();
				}
				if (row["Kh_Name"] != null)
				{
					model.Kh_Name = row["Kh_Name"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Yp_Code,Ph_Code,Tm_Code,Sy_Code,Rk_Code,Rk_Date,Ck_Code,Ck_Date,Yp_Xsdj,Yp_Exist,Rk_Sc_Finish,Ck_Sc_Finish,Kh_Code,Kh_Name ");
			strSql.Append(" FROM Zd_Yp4 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Yp_Code,Ph_Code,Tm_Code,Sy_Code,Rk_Code,Rk_Date,Ck_Code,Ck_Date,Yp_Xsdj,Yp_Exist,Rk_Sc_Finish,Ck_Sc_Finish,Kh_Code,Kh_Name ");
			strSql.Append(" FROM Zd_Yp4 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Zd_Yp4 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Sy_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_Yp4 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_Yp4";
			parameters[1].Value = "Sy_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 获取销售商品明细 - 根据销售单号
		/// </summary>
		/// <param name="ckCode">销售单号</param>
		/// <param name="Yp_Code">商品编码</param>
		/// <returns>商品明细数据集</returns>
		public DataSet GetSaleGoodsByCkCode(string ckCode, string Yp_Code)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT ");
			strSql.Append("y4.Yp_Code, ");
			strSql.Append("y4.Ck_Code, ");
			strSql.Append("y4.Ck_Date, ");
			strSql.Append("y4.Yp_Xsdj, ");
			strSql.Append("y4.Ck_Sl, ");
			strSql.Append("y4.Ck_Money, ");
			strSql.Append("y2.Yp_Name, ");
			strSql.Append("y2.Yp_Pzwh, ");
			strSql.Append("y2.Yp_Bzgg, ");
			strSql.Append("y2.Yp_Jx, ");
			strSql.Append("y2.Yp_Scqy, ");
			strSql.Append("y3.Yp_ScDate1, ");
			strSql.Append("y3.Yp_ScDate2 ");
			strSql.Append("FROM (select Yp_Code, Ck_Code, Ck_Date, Yp_Xsdj,Count(Sy_Code) as Ck_Sl,sum(Yp_Xsdj) as Ck_Money from Zd_Yp4 where Ck_Code = @Ck_Code Group by Yp_Code, Ck_Code,Ck_Date,Yp_Xsdj) y4 ");
			strSql.Append("INNER JOIN Zd_Yp3 y3 ON y4.Yp_Code = y3.Yp_Code ");
			strSql.Append("INNER JOIN Zd_Yp2 y2 ON y3.Xl_Code = y2.Xl_Code ");
			strSql.Append("WHERE y4.Ck_Code = @Ck_Code ");
			if (!string.IsNullOrEmpty(Yp_Code))
			{
				strSql.Append("AND y4.Yp_Code = @Yp_Code ");
			}
			strSql.Append("ORDER BY y4.Ck_Code");

			SqlParameter[] parameters = {
				new SqlParameter("@Ck_Code", SqlDbType.Char, 9),
				new SqlParameter("@Yp_Code", SqlDbType.Char, 11)
			};
			parameters[0].Value = ckCode;
			parameters[1].Value = Yp_Code;

			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
		}
		/// <summary>
		/// 获取追溯码 - 根据销售单号
		/// </summary>
		/// <param name="ckCode">销售单号</param>
		/// <param name="Yp_Code">商品编码</param>
		/// <returns>商品明细数据集</returns>
		public DataSet GetTraceCodeByCkCode(string ckCode, string Yp_Code)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT ");
			strSql.Append("y4.Sy_Code, ");
			strSql.Append("y4.Yp_Code, ");
			strSql.Append("y4.Ck_Code, ");
			strSql.Append("y4.Ck_Date, ");
			strSql.Append("y4.Yp_Xsdj, ");
			strSql.Append("1 as Ck_Sl, ");
			strSql.Append("y4.Yp_Xsdj, ");
			strSql.Append("y2.Yp_Name, ");
			strSql.Append("y2.Yp_Pzwh, ");
			strSql.Append("y2.Yp_Bzgg, ");
			strSql.Append("y2.Yp_Jx, ");
			strSql.Append("y2.Yp_Scqy, ");
			strSql.Append("y3.Yp_ScDate1, ");
			strSql.Append("y3.Yp_ScDate2 ");
			strSql.Append("FROM Zd_Yp4 y4 ");
			strSql.Append("INNER JOIN Zd_Yp3 y3 ON y4.Yp_Code = y3.Yp_Code ");
			strSql.Append("INNER JOIN Zd_Yp2 y2 ON y3.Xl_Code = y2.Xl_Code ");
			strSql.Append("WHERE y4.Ck_Code = @Ck_Code ");
			if (!string.IsNullOrEmpty(Yp_Code))
			{
				strSql.Append("AND y4.Yp_Code = @Yp_Code ");
			}
			strSql.Append("ORDER BY y4.Sy_Code");

			SqlParameter[] parameters = {
				new SqlParameter("@Ck_Code", SqlDbType.Char, 9),
				new SqlParameter("@Yp_Code", SqlDbType.Char, 11)
			};
			parameters[0].Value = ckCode;
			parameters[1].Value = Yp_Code;

			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
		}
		#endregion  ExtensionMethod
	}
}

