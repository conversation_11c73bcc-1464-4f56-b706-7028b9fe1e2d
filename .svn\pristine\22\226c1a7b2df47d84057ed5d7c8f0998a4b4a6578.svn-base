using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using BLL;
using Common.Delegate;
using Model;
using YdPublicFunction;
using Common;
using YdVar;

namespace YdBusiness
{
    public partial class Ys_Order2 : Common.BaseForm.DoubleFormRK1
    {
        private DataTable ZbTable;
        private DataRow ZbRow;
        public Common.Delegate.TransmitTxt ZbTransmitTxt = new TransmitTxt();
        private bool _frmInit = false;
        private BLL.BllDd1 _bllDd1 = new BllDd1();
        private BLL.BllDd2 _bllDd2 = new BllDd2();
        private Model.MdlDd1 _mdlDd1 = new MdlDd1();
        private decimal Ys_Money = 0;

        public Ys_Order2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();
            ZbRow = row;
            base.Insert = false; // 验收模块不允许新增
            ZbTable = table;
            TxtMemo.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void Ys_Order2_Load(object sender, EventArgs e)
        {
            _frmInit = false;
            FormInit();
            Zb_Show();
            BtnState();
            _frmInit = true;
            Thread thread;
            thread = new Thread(this.Cb_Show);
            thread.IsBackground = true;
            thread.Start();
        }

        #region 自定义函数

        #region 初始化函数
        private void FormInit()
        {
            base.BaseBtnClose = BtnClose;
            base.BaseBtnSave = BtnSave;
            base.BaseBtnComplete = BtnComplete;
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;

            BtnSave.Location = new Point(30, 1);
            BtnComplete.Location = new Point(BtnSave.Left + BtnSave.Width + 2, 1);
            BtnClose.Location = new Point(BtnComplete.Left + BtnComplete.Width + 2, 1);

            TxtCode.Enabled = false;
            NumYsMoney.Enabled = false;

            DtpDate.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            DtpDate.DisplayFormat = "yyyy-MM-dd HH:mm:ss";
            DtpDate.EditFormat = "yyyy-MM-dd HH:mm:ss";
            DtpDate.Enabled = false;

            // 初始化供应商下拉菜单
            comboGys1.Init();
            comboGys1.Enabled = false;

            myGrid1.Init_Grid();
            myGrid1.Init_Column("明细ID", "Dd_ID", 80, "中", "", false);
            myGrid1.Init_Column("药品编码", "Xl_Code", 100, "中", "", false);
            myGrid1.Init_Column("药品名称", "Yp_Name", 200, "左", "", false);
            myGrid1.Init_Column("批准文号", "Yp_Pzwh", 200, "左", "", false);
            myGrid1.Init_Column("生产企业", "Yp_Scqy", 300, "左", "", false);
            myGrid1.Init_Column("规格", "Yp_Zjgg", 120, "左", "", false);
            myGrid1.Init_Column("单位", "Yp_Zjdw", 60, "中", "", false);
            myGrid1.Init_Column("剂型", "Yp_Jx", 60, "中", "", false);
            myGrid1.Init_Column("订单数量", "Dd_Sl", 100, "右", "#,##0.00", false);
            myGrid1.Init_Column("订单单价", "Dd_Dj", 100, "右", "#,##0.00", false);
            myGrid1.Init_Column("订单金额", "Dd_Money", 120, "右", "#,##0.00", false);
            myGrid1.Init_Column("验收数量", "Ys_Sl", 100, "右", "#,##0.00", false);
            myGrid1.Init_Column("验收单价", "Ys_Dj", 100, "右", "#,##0.00", false);
            myGrid1.Init_Column("验收金额", "Ys_Money", 120, "右", "#,##0.00", false);
            myGrid1.Init_Column("验收状态", "Ys_Finish", 80, "中", "", false);
            myGrid1.Init_Column("备注", "Dd_Memo", 200, "左", "", false);
            myGrid1.AllowSort = true;
            myGrid1.AllowAddNew = false;
        }
        private void BtnState()
        {
            if (_mdlDd1.Dd_Finish.IsNullOrEmpty() || _mdlDd1.Dd_Finish == "0")
            {
                BtnSave.Enabled = true;
                BtnComplete.Enabled = true;
                BtnClose.Enabled = true;
                ControlEnable(false);
            }
            else
            {
                BtnSave.Enabled = false;
                BtnComplete.Enabled = false;
                BtnClose.Enabled = true;
                ControlEnable(false);
            }
        }
        private void ControlEnable(bool flag)
        {
            comboGys1.Enabled = false;
            DtpDate.Enabled = false;
            TxtMemo.Enabled = false;
            myGrid1.AllowAddNew = false;
        }
        #endregion

        #region  显示函数
        private void Zb_Show()
        {
            _mdlDd1 = _bllDd1.GetModel(ZbRow["Dd_Code"] + "");
            TxtCode.Text = _mdlDd1.Dd_Code;
            comboGys1.SelectedValue = _mdlDd1.Kh_Code;
            DtpDate.Value = _mdlDd1.Dd_Date ?? DateTime.Now;
            NumYsMoney.Value = _mdlDd1.Ys_Money ?? 0;
            TxtMemo.Text = _mdlDd1.Dd_Memo;
            ZbPictureShow(_mdlDd1.Dd_Finish);
        }
        private void ZbPictureShow(string Dd_Finish)
        {
            if (Dd_Finish == "1")
            {
                pictureBox1.Image = YdResources.StateRes.已完成;
            }
            if (Dd_Finish == "0")
            {
                pictureBox1.Image = YdResources.StateRes.未完成;
            }
            if (Dd_Finish == "")
            {
                pictureBox1.Image = YdResources.StateRes.新单;
            }

        }
        private void Cb_Show()
        {
            MyTable = _bllDd2.GetList($"Dd_Code='{_mdlDd1.Dd_Code}'").Tables[0];
            MyTable.TableName = "明细";
            //主表记录
            MyCm = (CurrencyManager)BindingContext[MyTable];
            myGrid1.BeginInvoke(new Action<DataTable>(p =>
            {
                myGrid1.DataTable = p;
                this.LblTotal.Text = "∑=" + p.Rows.Count;
            }), MyTable);
            DataSum("");
        }
        #endregion

        #region 检查语句
        //检查主表数据
        private bool ZbCheck()
        {
            if (!this.Insert && _bllDd1.GetRecordCount("Dd_Code='" + TxtCode.Text + "'") == 0)
            {
                MessageBox.Show("此订单已经被删除，无法继续操作!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            return true;
        }

        private bool ZbStateCheck(string state)
        {
            if (state == "验收")
            {
                if (_bllDd1.GetRecordCount("Dd_Code='" + TxtCode.Text + "' And Dd_Finish='1' ") > 0)
                {
                    MessageBox.Show("此订单已经完成验收!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
            }
            if (state == "从表修改")
            {
                if (_bllDd1.GetRecordCount("Dd_Code='" + TxtCode.Text + "' And Dd_Finish='1' ") > 0)
                {
                    MessageBox.Show("此订单已经完成验收!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
            }
            return true;
        }
        #endregion

        #region 按钮函数

        /// <summary>
        /// 保存验收数据
        /// </summary>
        /// <param name="showMsgbox">是否弹出提示框</param>
        /// <returns></returns>
        protected override bool DataSave(bool showMsgbox)
        {
            if (ZbCheck() == false) return false;
            if (showMsgbox == true && ZbStateCheck("验收") == false) return false;
            DataSum("");

            // 更新验收金额
            Zb_Edit();

            if (showMsgbox == true) MessageBox.Show("验收数据保存成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return true;
        }

        protected override void DataComplete()
        {
            if (ZbStateCheck("完成") == false) return;
            if (MessageBox.Show("是否完成此订单验收？", "提示:", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.No)
                return;
            if (DataSave(false) == false) return;

            // 更新订单完成状态
            if (_bllDd1.Complete(_mdlDd1.Dd_Code))
            {
                _mdlDd1.Dd_Finish = "1";
                ZbRow["Dd_Finish"] = "1";
                ZbPictureShow("1");
                BtnState();
                MessageBox.Show("订单验收已完成!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        #endregion

        #region 数据操作函数

        protected override void DataSum(string s)
        {
            Ys_Money = MyTable.Compute("Sum(Ys_Money)", "") == DBNull.Value ? 0 : Convert.ToDecimal(MyTable.Compute("Sum(Ys_Money)", ""));
            myGrid1.Columns["Ys_Money"].FooterText = string.Format("{0:####0.00##}", Ys_Money);
            NumYsMoney.BeginInvoke(new Action<decimal>(p => { NumYsMoney.Value = p; }), Ys_Money);
            LblTotal.BeginInvoke(new Action(() => { LblTotal.Text = "∑=" + base.MyTable.Rows.Count.ToString(); }));
            if (!_mdlDd1.Dd_Code.IsNullOrEmpty())
            {
                _mdlDd1.Ys_Money = Ys_Money;
                _bllDd1.Update(_mdlDd1);
                ZbRow["Ys_Money"] = Ys_Money;
            }
        }

        protected override void SubDataEdit()
        {
            bool subInsert;
            if ((myGrid1.Row + 1) > myGrid1.RowCount)
            {
                base.SubItemRow = base.MyTable.NewRow();
                subInsert = true;
            }
            else
            {
                base.SubItemRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
                subInsert = false;
            }

            Ys_Order3 f = new Ys_Order3(_mdlDd1.Dd_Code, subInsert, base.SubItemRow, base.MyTable);
            f.MyTransmitTxt = this.MyTransmitTxt;
            f.Owner = this;
            f.ShowDialog();

        }

        #region 主表
        //编辑记录
        private void Zb_Edit()
        {
            _mdlDd1.Ys_Money = Ys_Money;

            // 检查是否所有明细都已验收
            bool allAccepted = true;
            foreach (DataRow row in MyTable.Rows)
            {
                if (row["Ys_Finish"] == DBNull.Value || row["Ys_Finish"].ToString() != "True")
                {
                    allAccepted = false;
                    break;
                }
            }

            // 如果所有明细都已验收，自动设置主表验收状态为已完成
            if (allAccepted && MyTable.Rows.Count > 0)
            {
                _mdlDd1.Dd_Finish = "1";
                ZbRow["Dd_Finish"] = "1";
                ZbPictureShow("1");
            }

            _bllDd1.Update(_mdlDd1);
            ZbRow["Ys_Money"] = Ys_Money;
        }
        #endregion

        #endregion

        #endregion

        #region 控件动作

        #region 按钮动作

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #region Grid动作
        private void myGrid1_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    this.SubDataEdit();
                    break;
            }
        }
        #endregion

        #endregion


    }
}
