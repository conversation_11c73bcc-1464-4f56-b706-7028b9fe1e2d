﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace 药店管理系统
{
    public class DBUpdate
    {
        public static Dictionary<int, string> DicUpdateSql = new Dictionary<int, string>();

        public static void DbUpdate(string dbPwd, string dbIp, string dbName)
        {
            string m_Jg;
            foreach (var item in DicUpdateSql)
            {
                try
                {
                    m_Jg = Common.WinFormVar.Var.DbHelper.ExecuteSqlPrint(item.Value);
                    if (bool.Parse(m_Jg) == false)
                    {
                        string sqlQuery = $"osql.exe -U Sa -P {dbPwd} -S {dbIp} -d {dbName} -i 更新脚本\\{item.Key}.sql -o {item.Key}.txt";
                        string strRst = Common.Cmd.ExeCommand(sqlQuery);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        public static bool DBNeedUpdate()
        {
            if (DicUpdateSql.Count == 0) return false;
            return !bool.Parse(Common.WinFormVar.Var.DbHelper.ExecuteSqlPrint(DicUpdateSql.Values.Last()));
        }

        public static void AddSqlScript()
        {
            //操作员表增加角色代码
            DicUpdateSql.Add(20250724, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Zd_Czy') AND Name='RoleCode' )print 'True'else Print 'False'");
            //Zd_SbJc表Jc_Code字段长度改为2
            DicUpdateSql.Add(20250725, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Zd_SbJc')AND st.name = 'char' and sc.name = 'Jc_Code' and sc.length = '2')print 'True' else Print 'False'");
            //Zd_Jb表Jb_Code字段长度改为13
            DicUpdateSql.Add(20250730, "IF EXISTS (select st.name as 'TypeName',sc.name,sc.length from syscolumns sc,systypes st where sc.xtype=st.xtype and sc.id in(select id from sysobjects where xtype='U' and name='Zd_Jb')AND st.name = 'char' and sc.name = 'Jb_Code' and sc.length = '13')print 'True' else Print 'False'");
            //Yp_Bhg增加Yp_Code
            DicUpdateSql.Add(20250731, "IF EXISTS (SELECT top 1 * FROM syscolumns WHERE id=OBJECT_ID('Yp_Bhg') AND Name='Yp_Code' )print 'True'else Print 'False'");


        }
    }
}