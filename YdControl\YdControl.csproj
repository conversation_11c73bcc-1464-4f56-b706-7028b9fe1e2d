﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{186009E9-7A04-4519-A696-79E57BB80B4E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>YdControl</RootNamespace>
    <AssemblyName>YdControl</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.C1List.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.ReportViewer.WinForms, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ComboRole.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboRole.Designer.cs">
      <DependentUpon>ComboRole.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboCzy.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboCzy.Designer.cs">
      <DependentUpon>ComboCzy.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboGys.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboGys.Designer.cs">
      <DependentUpon>ComboGys.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboYpPh.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboYpPh.Designer.cs">
      <DependentUpon>ComboYpPh.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SingleBlfy.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleBlfy.Designer.cs">
      <DependentUpon>SingleBlfy.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleBlfyJg.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleBlfyJg.Designer.cs">
      <DependentUpon>SingleBlfyJg.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleBlfyYx.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleBlfyYx.Designer.cs">
      <DependentUpon>SingleBlfyYx.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleCzyLb.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleCzyLb.Designer.cs">
      <DependentUpon>SingleCzyLb.cs</DependentUpon>
    </Compile>
    <Compile Include="SinglePj.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SinglePj.Designer.cs">
      <DependentUpon>SinglePj.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleQyXz.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleQyXz.Designer.cs">
      <DependentUpon>SingleQyXz.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleSbZt.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleSbZt.Designer.cs">
      <DependentUpon>SingleSbZt.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleSex.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleSex.Designer.cs">
      <DependentUpon>SingleSex.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboTsyp.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboTsyp.Designer.cs">
      <DependentUpon>ComboTsyp.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboYpdl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboYpdl.Designer.cs">
      <DependentUpon>ComboYpdl.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboYpjx.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboYpjx.Designer.cs">
      <DependentUpon>ComboYpjx.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboCctj.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboCctj.Designer.cs">
      <DependentUpon>ComboCctj.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboCfdw.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboCfdw.Designer.cs">
      <DependentUpon>ComboCfdw.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboHj.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboHj.Designer.cs">
      <DependentUpon>ComboHj.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboPay.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboPay.Designer.cs">
      <DependentUpon>ComboPay.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboYp.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboYp.Designer.cs">
      <DependentUpon>ComboYp.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleTjjd.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleTjjd.Designer.cs">
      <DependentUpon>SingleTjjd.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleYesNo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleYesNo.Designer.cs">
      <DependentUpon>SingleYesNo.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BLL\BLL.csproj">
      <Project>{46b795c2-6efa-41e6-948e-66f92e591b6a}</Project>
      <Name>BLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92e350a0-3691-4b8d-a07e-ebb0f10e6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12bf4168-d60e-4a6c-85bf-926130ee6a6d}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\DbProviderFactory\DbProviderFactory.csproj">
      <Project>{fdf5d6d6-d281-4884-a81a-d0c49c2f3bc7}</Project>
      <Name>DbProviderFactory</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdEnum\YdEnum.csproj">
      <Project>{2658ea9e-035b-43e4-b40f-6cebe092f702}</Project>
      <Name>YdEnum</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="ComboYpPh.resx">
      <DependentUpon>ComboYpPh.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="ComboCzy.resx">
      <DependentUpon>ComboCzy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboGys.resx">
      <DependentUpon>ComboGys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleBlfy.resx">
      <DependentUpon>SingleBlfy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleBlfyJg.resx">
      <DependentUpon>SingleBlfyJg.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleBlfyYx.resx">
      <DependentUpon>SingleBlfyYx.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleCzyLb.resx">
      <DependentUpon>SingleCzyLb.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SinglePj.resx">
      <DependentUpon>SinglePj.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleQyXz.resx">
      <DependentUpon>SingleQyXz.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleSbZt.resx">
      <DependentUpon>SingleSbZt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleSex.resx">
      <DependentUpon>SingleSex.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboTsyp.resx">
      <DependentUpon>ComboTsyp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboYpdl.resx">
      <DependentUpon>ComboYpdl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboYpjx.resx">
      <DependentUpon>ComboYpjx.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboCctj.resx">
      <DependentUpon>ComboCctj.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboCfdw.resx">
      <DependentUpon>ComboCfdw.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboHj.resx">
      <DependentUpon>ComboHj.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboPay.resx">
      <DependentUpon>ComboPay.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboYp.resx">
      <DependentUpon>ComboYp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleTjjd.resx">
      <DependentUpon>SingleTjjd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleYesNo.resx">
      <DependentUpon>SingleYesNo.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>