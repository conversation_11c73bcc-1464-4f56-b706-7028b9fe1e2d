using System;
using System.Data;
using System.Windows.Forms;
using BLL;
using CustomControl;

namespace YdControl
{
    public partial class ComboYpPh : MyDtComobo
    {
        public ComboYpPh()
        {
            InitializeComponent();
        }

        public void Init(string strWhere = " 1=1")
        {
            if (string.IsNullOrEmpty(strWhere))
            {
                strWhere = " 1=1";
            }

            // 使用三层架构获取药品批号与药品信息关联数据
            BLL.BllZd_Yp3 _bllZd_Yp3 = new BllZd_Yp3();
            this.DataView = _bllZd_Yp3.GetListWithYpInfo(strWhere).Tables[0].DefaultView;

            this.Init_Colum("Yp_Name", "药品名称", 200, "左");
            this.Init_Colum("Yp_Scph", "生产批号", 120, "左");
            this.Init_Colum("Yp_Jc", "简称", 80, "左");
            this.Init_Colum("Yp_Code", "批号编码", 0, "左");
            this.Init_Colum("Yp_Zjgg", "规格", 120, "左");
            this.Init_Colum("Yp_Zjdw", "单位", 60, "左");
            this.Init_Colum("Yp_Scqy", "生产企业", 150, "左");
            this.Init_Colum("Yp_ScDate1", "生产日期", 100, "中");
            this.Init_Colum("Yp_ScDate2", "有效期至", 100, "中");
            this.Init_Colum("Yp_Pzwh", "批准文号", 150, "左");

            this.DisplayMember = "Yp_Name";
            this.ValueMember = "Yp_Code";

            int width = 800;
            if (this.Width - (int)this.CaptainWidth > width) width = this.Width - (int)this.CaptainWidth;
            DroupDownWidth = width;
            this.MaxDropDownItems = 15;
            this.SelectedIndex = -1;
            this.RowFilterTextNull = "";
            this.ItemHeight = 20;
            this.RowFilterNotTextNull = "Yp_Code+isnull(Yp_Jc,'')+Yp_Name+isnull(Yp_Scph,'')+isnull(Yp_Pzwh,'')";
        }
    }
}
