BEGIN TRANSACTION; -- 使用事务，如果任何一步失败，可以回滚所有操作

BEGIN TRY
ALTER TABLE dbo.Yk_Rk1 ADD Rk_Ok VARCHAR(50) DEFAULT '未完成'


/****** Object:  Table [dbo].[Yk_Rk2]    Script Date: 2025-07-31 14:32:45 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Yk_Rk2](
	[Rk_Id] [int] IDENTITY(1,1) NOT NULL,
	[Rk_Code] [char](9) NOT NULL,
	[Yp_Code] [char](11) NULL,
	[Yp_Scph] [varchar](20) NULL,
	[Yp_ScDate1] [smalldatetime] NULL,
	[Yp_ScDate2] [smalldatetime] NULL,
	[Rk_Sl] [numeric](12, 4) NULL,
	[Rk_Dj] [numeric](18, 6) NULL,
	[Rk_Money] [numeric](18, 4) NULL,
	[Rk_Memo] [varchar](50) NULL,
 CONSTRAINT [PK_Yk_Rk2] PRIMARY KEY CLUSTERED 
(
	[Rk_Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Yk_RkDrugtracinfo]    Script Date: 2025-07-31 14:32:45 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Yk_RkDrugtracinfo](
	[Rk_Id] [int] NOT NULL,
	[drug_trac_codg] [varchar](100) NOT NULL,
 CONSTRAINT [PK_Yk_Rk_Drugtracinfo] PRIMARY KEY CLUSTERED 
(
	[Rk_Id] ASC,
	[drug_trac_codg] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[Yk_Rk2] ADD  CONSTRAINT [DF_Yk_Rk2_Rk_Sl]  DEFAULT ((0)) FOR [Rk_Sl]
GO
ALTER TABLE [dbo].[Yk_Rk2] ADD  CONSTRAINT [DF_Yk_Rk2_Rk_Dj]  DEFAULT ((0)) FOR [Rk_Dj]
GO
ALTER TABLE [dbo].[Yk_Rk2] ADD  CONSTRAINT [DF_Yk_Rk2_Rk_Money]  DEFAULT ((0)) FOR [Rk_Money]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_数量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Rk2', @level2type=N'COLUMN',@level2name=N'Rk_Sl'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_单价' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Rk2', @level2type=N'COLUMN',@level2name=N'Rk_Dj'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_金额' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Rk2', @level2type=N'COLUMN',@level2name=N'Rk_Money'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Rk2', @level2type=N'COLUMN',@level2name=N'Rk_Memo'
GO
    COMMIT TRANSACTION;
    PRINT '表 Zd_Jb 的主键列 Jb_Code 修改成功！';

END TRY
BEGIN CATCH
    -- 如果任何步骤发生错误，回滚所有操作
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION;

    -- 打印错误信息
    PRINT '操作失败，事务已回滚。';
    PRINT '错误信息: ' + ERROR_MESSAGE();
END CATCH;


