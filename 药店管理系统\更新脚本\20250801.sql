-- 数据库更新脚本 - 20250801
-- 功能：添加 Yk_Rk1.Rk_Ok 列，创建 Yk_Rk2 和 Yk_RkDrugtracinfo 表

BEGIN TRANSACTION;

BEGIN TRY
    -- 1. 给 Yk_Rk1 表添加 Rk_Ok 列
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Yk_Rk1') AND name = 'Rk_Ok')
    BEGIN
        ALTER TABLE dbo.Yk_Rk1 ADD Rk_Ok VARCHAR(50) DEFAULT '未完成';
        PRINT '已成功添加 Yk_Rk1.Rk_Ok 列';
    END
    ELSE
    BEGIN
        PRINT 'Yk_Rk1.Rk_Ok 列已存在，跳过添加';
    END

    -- 2. 创建 Yk_Rk2 表
    IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('dbo.Yk_Rk2') AND type in ('U'))
    BEGIN
        CREATE TABLE [dbo].[Yk_Rk2](
            [Rk_Id] [int] IDENTITY(1,1) NOT NULL,
            [Rk_Code] [char](9) NOT NULL,
            [Yp_Code] [char](11) NULL,
            [Yp_Scph] [varchar](20) NULL,
            [Yp_ScDate1] [smalldatetime] NULL,
            [Yp_ScDate2] [smalldatetime] NULL,
            [Rk_Sl] [numeric](12, 4) NULL CONSTRAINT [DF_Yk_Rk2_Rk_Sl] DEFAULT ((0)),
            [Rk_Dj] [numeric](18, 6) NULL CONSTRAINT [DF_Yk_Rk2_Rk_Dj] DEFAULT ((0)),
            [Rk_Money] [numeric](18, 4) NULL CONSTRAINT [DF_Yk_Rk2_Rk_Money] DEFAULT ((0)),
            [Rk_Memo] [varchar](50) NULL,
            CONSTRAINT [PK_Yk_Rk2] PRIMARY KEY CLUSTERED ([Rk_Id] ASC)
                WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, 
                      ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
        ) ON [PRIMARY];
        
        -- 添加列注释
        EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_数量', 
            @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Rk2', 
            @level2type=N'COLUMN',@level2name=N'Rk_Sl';
        EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_单价', 
            @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Rk2', 
            @level2type=N'COLUMN',@level2name=N'Rk_Dj';
        EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_金额', 
            @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Rk2', 
            @level2type=N'COLUMN',@level2name=N'Rk_Money';
        EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'药房入库_备注', 
            @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Yk_Rk2', 
            @level2type=N'COLUMN',@level2name=N'Rk_Memo';
        
        PRINT '已成功创建 Yk_Rk2 表';
    END
    ELSE
    BEGIN
        PRINT 'Yk_Rk2 表已存在，跳过创建';
    END

    -- 3. 创建 Yk_RkDrugtracinfo 表
    IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('dbo.Yk_RkDrugtracinfo') AND type in ('U'))
    BEGIN
        CREATE TABLE [dbo].[Yk_RkDrugtracinfo](
            [Rk_Id] [int] NOT NULL,
            [drug_trac_codg] [varchar](100) NOT NULL,
            CONSTRAINT [PK_Yk_Rk_Drugtracinfo] PRIMARY KEY CLUSTERED ([Rk_Id] ASC, [drug_trac_codg] ASC)
                WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, 
                      ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
        ) ON [PRIMARY];
        
        PRINT '已成功创建 Yk_RkDrugtracinfo 表';
    END
    ELSE
    BEGIN
        PRINT 'Yk_RkDrugtracinfo 表已存在，跳过创建';
    END

    COMMIT TRANSACTION;
    PRINT '数据库更新成功完成！';

END TRY
BEGIN CATCH
    -- 如果任何步骤发生错误，回滚所有操作
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION;

    -- 打印错误信息
    PRINT '操作失败，事务已回滚。';
    PRINT '错误信息: ' + ERROR_MESSAGE();
    PRINT '错误行号: ' + CAST(ERROR_LINE() AS VARCHAR(10));
END CATCH;
