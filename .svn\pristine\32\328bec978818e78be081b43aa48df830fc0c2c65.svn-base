using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using YdPublicFunction;

namespace YdGSP
{
    public partial class Yp_Bhg1 : Common.BaseForm.BaseDict1
    {
        BLL.BllYp_Bhg _bllYp_Bhg = new BllYp_Bhg();

        public Yp_Bhg1()
        {
            InitializeComponent();
        }

        private void Yp_Bhg1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            this.FormInit();
            this.DataInit();

            TxtFilter.GotFocus += new System.EventHandler(base.InputEn);
        }
        #region 自定义函数

        private void FormInit()
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("不合格编码", "Bhg_Code", 120, "中", "", false);
            myGrid1.Init_Column("锁定日期", "Bhg_Date", 100, "中", "yyyy-MM-dd", false);
            myGrid1.Init_Column("药品批准文号", "Yp_Pzwh", 150, "左", "", false);
            myGrid1.Init_Column("生产批号", "Yp_Scph", 120, "左", "", false);
            myGrid1.Init_Column("药品名称", "Yp_Name", 200, "左", "", false);
            myGrid1.Init_Column("简称", "Yp_Jc", 80, "左", "", false);
            myGrid1.Init_Column("剂型", "Yp_Jx", 80, "左", "", false);
            myGrid1.Init_Column("规格", "Yp_Zjgg", 120, "左", "", false);
            myGrid1.Init_Column("包装规格", "Yp_Bzgg", 100, "左", "", false);
            myGrid1.Init_Column("生产企业", "Yp_Scqy", 150, "左", "", false);
            myGrid1.Init_Column("备注", "Bhg_Memo", 200, "左", "", false);
            myGrid1.AllowSort = true;
        }

        private void DataInit()
        {
            base.MyTable = _bllYp_Bhg.GetList("").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["Bhg_Code"] };
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            this.myGrid1.DataTable = base.MyTable;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            base.MyView = (DataView)base.MyCm.List;
        }

        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            Yp_Bhg2 vform = new Yp_Bhg2(base.Insert, base.MyRow, base.MyTable);
            vform.MyTransmitTxt = base.MyTransmitTxt;
            vform.MyTransmitDataRow = base.MyTransmitDataRow;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }
        }

        protected override void DataDelete()
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除不合格药品锁定记录：" + this.myGrid1.Columns["Yp_Name"].Value, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                return;
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            _bllYp_Bhg.Delete(base.MyRow["Bhg_Code"].ToString());
            myGrid1.Delete();
            base.MyTable.AcceptChanges();
            this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();
        }

        private void DataRefresh()
        {
            this.DataInit();
        }

        #endregion

        #region 控件动作

        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(true);
        }

        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataDelete();
        }

        private void CmdRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataRefresh();
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            base.DataFilter("Bhg_Code+Yp_Name+Yp_Jc+Yp_Scph+Yp_Pzwh", TxtFilter.Text.Trim());
        }
        #endregion
    }
}
