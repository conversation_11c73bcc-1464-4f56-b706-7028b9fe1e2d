using BLL;
using Model;
using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace YdBusiness
{
    public partial class Dd_Order3 : Common.BaseForm.DoubleFormRK2
    {
        private string dd_Code;
        private bool _frmInit = false;
        private BllDd2 _bllDd2 = new BllDd2();

        public Dd_Order3(string dd_Code, bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();
            this.dd_Code = dd_Code;
            base.Insert = insert;
            base.SubItemRow = row;
            base.MyTable = table;
            comboYp1.GotFocus += new System.EventHandler(base.InputEn);
        }

        private void Dd_Order3_Load(object sender, EventArgs e)
        {
            _frmInit = false;
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.SubItemRow);
            _frmInit = true;
        }

        #region 初始化函数
        private void FormInit()
        {
            TxtJx.Enabled = false;
            TxtZjgg.Enabled = false;
            TxtBzgg.Enabled = false;
            TxtZjdw.Enabled = false;
            TxtScqy.Enabled = false;
            NumMoney.Enabled = false;
            // 初始化药品下拉菜单
            comboYp1.Init();

            // 设置按钮位置
            panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);
        }
        #endregion

        #region 自定义函数
        private void DataClear()
        {
            base.Insert = true;
            comboYp1.Enabled = true;
            comboYp1.SelectedIndex = -1;

            TxtJx.Text = "";
            TxtZjgg.Text = "";
            TxtBzgg.Text = "";
            TxtZjdw.Text = "";
            TxtScqy.Text = "";
            NumSl.Value = 0;
            NumDj.Value = 0;
            NumMoney.Value = 0;
            TxtMemo.Text = "";

            comboYp1.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;

            comboYp1.Enabled = false;
            comboYp1.SelectedValue = row["Xl_Code"] + "";

            NumSl.Value = decimal.Parse(row["Dd_Sl"] + "");
            NumDj.Value = decimal.Parse(row["Dd_Dj"] + "");
            NumMoney.Value = decimal.Parse(row["Dd_Money"] + "");
            TxtMemo.Text = row["Dd_Memo"] + "";

            NumSl.Select();
        }

        private bool DataCheck()
        {
            if (CustomControl.Func.NotAllowEmpty(comboYp1)) return false;
            if (CustomControl.Func.NotAllowEmpty(NumSl)) return false;
            if (Convert.ToDecimal(NumSl.Value) == 0)
            {
                MessageBox.Show("订单数量不能为0！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                NumSl.Select();
                return false;
            }
            if (Convert.ToDecimal(NumDj.Value) == 0)
            {
                MessageBox.Show("订单单价不能为0！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                NumDj.Select();
                return false;
            }
            if (Insert)
            {
                if (MyTable.AsEnumerable().Where(p => p["Xl_Code"].ToString() == comboYp1.SelectedValue + "").Count() > 0)
                {
                    MessageBox.Show($"请勿重复添加此药品 {comboYp1.Text}!", "提示", MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                    comboYp1.Select();
                    return false;
                }
            }
            return true;
        }

        private void DataAdd()
        {
            base.SubItemRow = base.MyTable.NewRow();

            SubItemRow["Yp_Name"] = comboYp1.Text;
            SubItemRow["Dd_Code"] = dd_Code;
            SubItemRow["Xl_Code"] = comboYp1.SelectedValue + "";
            SubItemRow["Dd_Sl"] = NumSl.Value;
            SubItemRow["Dd_Dj"] = NumDj.Value;
            SubItemRow["Dd_Money"] = Common.MathFormula.Multiply(SubItemRow["Dd_Sl"], SubItemRow["Dd_Dj"]);
            SubItemRow["Dd_Memo"] = TxtMemo.Text;
            SubItemRow["Yp_Code"] = comboYp1.SelectedValue + "";

            //数据保存
            try
            {
                MdlDd2 mdlDd2 = Common.DataTableToList.ToModel<MdlDd2>(SubItemRow);
                SubItemRow["Yp_Jx"] = comboYp1.Columns["Yp_Jx"].Value + "";
                SubItemRow["Yp_Zjgg"] = comboYp1.Columns["Yp_Zjgg"].Value + "";
                SubItemRow["Yp_Bzgg"] = comboYp1.Columns["Yp_Bzgg"].Value + "";
                SubItemRow["Yp_Zjdw"] = comboYp1.Columns["Yp_Zjdw"].Value + "";
                SubItemRow["Yp_Scqy"] = comboYp1.Columns["Yp_Scqy"].Value + "";
                SubItemRow["Yp_Bzzhb"] = comboYp1.Columns["Yp_Bzzhb"].Value + "";
                SubItemRow["Yp_Otc"] = comboYp1.Columns["Yp_Otc"].Value + "";
                SubItemRow["Yp_Lc"] = comboYp1.Columns["Yp_Lc"].Value + "";
                SubItemRow["Yp_Pzwh"] = comboYp1.Columns["Yp_Pzwh"].Value + "";
                _bllDd2.Add(mdlDd2);
                base.MyTable.Rows.Add(base.SubItemRow);
                base.SubItemRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("增加成功");
                comboYp1.Focus();
                DataClear();
            }
        }

        private void DataEdit()
        {
            MdlDd2 mdlDd2 = _bllDd2.GetModel(Convert.ToInt32(SubItemRow["Dd_ID"]));
            SubItemRow["Yp_Name"] = comboYp1.Text;
            SubItemRow["Dd_Code"] = dd_Code;
            SubItemRow["Xl_Code"] = comboYp1.SelectedValue + "";
            SubItemRow["Dd_Sl"] = NumSl.Value;
            SubItemRow["Dd_Dj"] = NumDj.Value;
            SubItemRow["Dd_Money"] = Common.MathFormula.Multiply(SubItemRow["Dd_Sl"], SubItemRow["Dd_Dj"]);
            SubItemRow["Dd_Memo"] = TxtMemo.Text;
            SubItemRow["Yp_Code"] = comboYp1.SelectedValue + "";

            //数据保存
            try
            {
                Common.DataTableToList.ToModel(SubItemRow, mdlDd2);
                SubItemRow["Yp_Jx"] = comboYp1.Columns["Yp_Jx"].Value + "";
                SubItemRow["Yp_Zjgg"] = comboYp1.Columns["Yp_Zjgg"].Value + "";
                SubItemRow["Yp_Bzgg"] = comboYp1.Columns["Yp_Bzgg"].Value + "";
                SubItemRow["Yp_Zjdw"] = comboYp1.Columns["Yp_Zjdw"].Value + "";
                SubItemRow["Yp_Scqy"] = comboYp1.Columns["Yp_Scqy"].Value + "";
                SubItemRow["Yp_Bzzhb"] = comboYp1.Columns["Yp_Bzzhb"].Value + "";
                SubItemRow["Yp_Otc"] = comboYp1.Columns["Yp_Otc"].Value + "";
                SubItemRow["Yp_Lc"] = comboYp1.Columns["Yp_Lc"].Value + "";
                SubItemRow["Yp_Pzwh"] = comboYp1.Columns["Yp_Pzwh"].Value + "";
                _bllDd2.Update(mdlDd2);
                base.SubItemRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                comboYp1.Focus();
            }
        }

        private void ComputeMoney()
        {
            NumMoney.Value = Common.MathFormula.Multiply(NumSl.Value, NumDj.Value);
        }


        #endregion

        #region 事件处理
        private void NumSl_ValueChanged(object sender, EventArgs e)
        {
            ComputeMoney();
        }

        private void NumDj_ValueChanged(object sender, EventArgs e)
        {
            ComputeMoney();
        }



        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck())
            {
                if (base.Insert)
                {
                    DataAdd();
                }
                else
                {
                    DataEdit();
                }
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void comboYp1_RowChange(object sender, EventArgs e)
        {
            if (comboYp1.WillChangeToValue == null)
            {
                TxtJx.Text = "";
                TxtZjgg.Text = "";
                TxtBzgg.Text = "";
                TxtZjdw.Text = "";
                TxtScqy.Text = "";
            }
            else
            {
                TxtJx.Text = comboYp1.Columns["Yp_Jx"].Value + "";
                TxtZjgg.Text = comboYp1.Columns["Yp_Zjgg"].Value + "";
                TxtBzgg.Text = comboYp1.Columns["Yp_Bzgg"].Value + "";
                TxtZjdw.Text = comboYp1.Columns["Yp_Zjdw"].Value + "";
                TxtScqy.Text = comboYp1.Columns["Yp_Scqy"].Value + "";
            }
        }
        #endregion
    }
}
