﻿/**  版本信息模板在安装目录下，可自行修改。
* DalZd_Yd_ZlFzr.cs
*
* 功 能： N/A
* 类 名： DalZd_Yd_ZlFzr
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:23   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalZd_Yd_ZlFzr
	/// </summary>
	public partial class DalZd_Yd_ZlFzr : IDalZd_Yd_ZlFzr
	{
		public DalZd_Yd_ZlFzr()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Zlfzr_Code)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from Zd_Yd_ZlFzr");
			strSql.Append(" where Zlfzr_Code=@Zlfzr_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Zlfzr_Code", SqlDbType.Char,7)           };
			parameters[0].Value = Zlfzr_Code;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlZd_Yd_ZlFzr model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into Zd_Yd_ZlFzr(");
			strSql.Append("Zlfzr_Code,Zlfzr_Name,Zlfzr_Sfzh,Zlfzr_Xl,Zlfzr_Zc,Zlfzr_Gznx,Zlfzr_Sgz,Zlfzr_Memo)");
			strSql.Append(" values (");
			strSql.Append("@Zlfzr_Code,@Zlfzr_Name,@Zlfzr_Sfzh,@Zlfzr_Xl,@Zlfzr_Zc,@Zlfzr_Gznx,@Zlfzr_Sgz,@Zlfzr_Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@Zlfzr_Code", SqlDbType.Char,7),
					new SqlParameter("@Zlfzr_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Zlfzr_Sfzh", SqlDbType.VarChar,18),
					new SqlParameter("@Zlfzr_Xl", SqlDbType.VarChar,50),
					new SqlParameter("@Zlfzr_Zc", SqlDbType.VarChar,50),
					new SqlParameter("@Zlfzr_Gznx", SqlDbType.VarChar,50),
					new SqlParameter("@Zlfzr_Sgz", SqlDbType.VarChar,50),
					new SqlParameter("@Zlfzr_Memo", SqlDbType.VarChar,200)};
			parameters[0].Value = model.Zlfzr_Code;
			parameters[1].Value = model.Zlfzr_Name;
			parameters[2].Value = model.Zlfzr_Sfzh;
			parameters[3].Value = model.Zlfzr_Xl;
			parameters[4].Value = model.Zlfzr_Zc;
			parameters[5].Value = model.Zlfzr_Gznx;
			parameters[6].Value = model.Zlfzr_Sgz;
			parameters[7].Value = model.Zlfzr_Memo;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlZd_Yd_ZlFzr model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Zd_Yd_ZlFzr set ");
			strSql.Append("Zlfzr_Name=@Zlfzr_Name,");
			strSql.Append("Zlfzr_Sfzh=@Zlfzr_Sfzh,");
			strSql.Append("Zlfzr_Xl=@Zlfzr_Xl,");
			strSql.Append("Zlfzr_Zc=@Zlfzr_Zc,");
			strSql.Append("Zlfzr_Gznx=@Zlfzr_Gznx,");
			strSql.Append("Zlfzr_Sgz=@Zlfzr_Sgz,");
			strSql.Append("Zlfzr_Memo=@Zlfzr_Memo");
			strSql.Append(" where Zlfzr_Code=@Zlfzr_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Zlfzr_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Zlfzr_Sfzh", SqlDbType.VarChar,18),
					new SqlParameter("@Zlfzr_Xl", SqlDbType.VarChar,50),
					new SqlParameter("@Zlfzr_Zc", SqlDbType.VarChar,50),
					new SqlParameter("@Zlfzr_Gznx", SqlDbType.VarChar,50),
					new SqlParameter("@Zlfzr_Sgz", SqlDbType.VarChar,50),
					new SqlParameter("@Zlfzr_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Zlfzr_Code", SqlDbType.Char,7)};
			parameters[0].Value = model.Zlfzr_Name;
			parameters[1].Value = model.Zlfzr_Sfzh;
			parameters[2].Value = model.Zlfzr_Xl;
			parameters[3].Value = model.Zlfzr_Zc;
			parameters[4].Value = model.Zlfzr_Gznx;
			parameters[5].Value = model.Zlfzr_Sgz;
			parameters[6].Value = model.Zlfzr_Memo;
			parameters[7].Value = model.Zlfzr_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Zlfzr_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_Yd_ZlFzr ");
			strSql.Append(" where Zlfzr_Code=@Zlfzr_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Zlfzr_Code", SqlDbType.Char,7)           };
			parameters[0].Value = Zlfzr_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Zlfzr_Codelist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_Yd_ZlFzr ");
			strSql.Append(" where Zlfzr_Code in (" + Zlfzr_Codelist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_Yd_ZlFzr GetModel(string Zlfzr_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Zlfzr_Code,Zlfzr_Name,Zlfzr_Sfzh,Zlfzr_Xl,Zlfzr_Zc,Zlfzr_Gznx,Zlfzr_Sgz,Zlfzr_Memo from Zd_Yd_ZlFzr ");
			strSql.Append(" where Zlfzr_Code=@Zlfzr_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Zlfzr_Code", SqlDbType.Char,7)           };
			parameters[0].Value = Zlfzr_Code;

			Model.MdlZd_Yd_ZlFzr model = new Model.MdlZd_Yd_ZlFzr();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_Yd_ZlFzr DataRowToModel(DataRow row)
		{
			Model.MdlZd_Yd_ZlFzr model = new Model.MdlZd_Yd_ZlFzr();
			if (row != null)
			{
				if (row["Zlfzr_Code"] != null)
				{
					model.Zlfzr_Code = row["Zlfzr_Code"].ToString();
				}
				if (row["Zlfzr_Name"] != null)
				{
					model.Zlfzr_Name = row["Zlfzr_Name"].ToString();
				}
				if (row["Zlfzr_Sfzh"] != null)
				{
					model.Zlfzr_Sfzh = row["Zlfzr_Sfzh"].ToString();
				}
				if (row["Zlfzr_Xl"] != null)
				{
					model.Zlfzr_Xl = row["Zlfzr_Xl"].ToString();
				}
				if (row["Zlfzr_Zc"] != null)
				{
					model.Zlfzr_Zc = row["Zlfzr_Zc"].ToString();
				}
				if (row["Zlfzr_Gznx"] != null)
				{
					model.Zlfzr_Gznx = row["Zlfzr_Gznx"].ToString();
				}
				if (row["Zlfzr_Sgz"] != null)
				{
					model.Zlfzr_Sgz = row["Zlfzr_Sgz"].ToString();
				}
				if (row["Zlfzr_Memo"] != null)
				{
					model.Zlfzr_Memo = row["Zlfzr_Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Zlfzr_Code,Zlfzr_Name,Zlfzr_Sfzh,Zlfzr_Xl,Zlfzr_Zc,Zlfzr_Gznx,Zlfzr_Sgz,Zlfzr_Memo ");
			strSql.Append(" FROM Zd_Yd_ZlFzr ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Zlfzr_Code,Zlfzr_Name,Zlfzr_Sfzh,Zlfzr_Xl,Zlfzr_Zc,Zlfzr_Gznx,Zlfzr_Sgz,Zlfzr_Memo ");
			strSql.Append(" FROM Zd_Yd_ZlFzr ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Zd_Yd_ZlFzr ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Zlfzr_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_Yd_ZlFzr T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_Yd_ZlFzr";
			parameters[1].Value = "Zlfzr_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = (string)(Common.WinFormVar.Var.DbHelper.F_MaxCode("SELECT MAX(Zlfzr_Code) FROM Zd_Yd_ZlFzr where LEN(Zlfzr_Code)=" + length, length));
			return max;
		}

		#endregion  ExtensionMethod
	}
}

