﻿/**  版本信息模板在安装目录下，可自行修改。
* DalZd_Yp3.cs
*
* 功 能： N/A
* 类 名： DalZd_Yp3
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:27   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalZd_Yp3
	/// </summary>
	public partial class DalZd_Yp3 : IDalZd_Yp3
	{
		public DalZd_Yp3()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Yp_Code)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from Zd_Yp3");
			strSql.Append(" where Yp_Code=@Yp_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yp_Code", SqlDbType.Char,11)         };
			parameters[0].Value = Yp_Code;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlZd_Yp3 model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into Zd_Yp3(");
			strSql.Append("Xl_Code,Yp_Code,Yp_Scph,Yp_ScDate1,Yp_ScDate2,Yp_Memo,Yp_Count,Sc_Finish,Rk_Sl)");
			strSql.Append(" values (");
			strSql.Append("@Xl_Code,@Yp_Code,@Yp_Scph,@Yp_ScDate1,@Yp_ScDate2,@Yp_Memo,@Yp_Count,@Sc_Finish,@Rk_Sl)");
			SqlParameter[] parameters = {
					new SqlParameter("@Xl_Code", SqlDbType.Char,8),
					new SqlParameter("@Yp_Code", SqlDbType.Char,11),
					new SqlParameter("@Yp_Scph", SqlDbType.VarChar,20),
					new SqlParameter("@Yp_ScDate1", SqlDbType.SmallDateTime),
					new SqlParameter("@Yp_ScDate2", SqlDbType.SmallDateTime),
					new SqlParameter("@Yp_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Yp_Count", SqlDbType.Int,4),
					new SqlParameter("@Sc_Finish", SqlDbType.Bit,1),
					new SqlParameter("@Rk_Sl", SqlDbType.Int,4)};
			parameters[0].Value = model.Xl_Code;
			parameters[1].Value = model.Yp_Code;
			parameters[2].Value = model.Yp_Scph;
			parameters[3].Value = model.Yp_ScDate1;
			parameters[4].Value = model.Yp_ScDate2;
			parameters[5].Value = model.Yp_Memo;
			parameters[6].Value = model.Yp_Count;
			parameters[7].Value = model.Sc_Finish;
			parameters[8].Value = model.Rk_Sl;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlZd_Yp3 model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Zd_Yp3 set ");
			strSql.Append("Xl_Code=@Xl_Code,");
			strSql.Append("Yp_Scph=@Yp_Scph,");
			strSql.Append("Yp_ScDate1=@Yp_ScDate1,");
			strSql.Append("Yp_ScDate2=@Yp_ScDate2,");
			strSql.Append("Yp_Memo=@Yp_Memo,");
			strSql.Append("Yp_Count=@Yp_Count,");
			strSql.Append("Sc_Finish=@Sc_Finish,");
			strSql.Append("Rk_Sl=@Rk_Sl");
			strSql.Append(" where Yp_Code=@Yp_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xl_Code", SqlDbType.Char,8),
					new SqlParameter("@Yp_Scph", SqlDbType.VarChar,20),
					new SqlParameter("@Yp_ScDate1", SqlDbType.SmallDateTime),
					new SqlParameter("@Yp_ScDate2", SqlDbType.SmallDateTime),
					new SqlParameter("@Yp_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Yp_Count", SqlDbType.Int,4),
					new SqlParameter("@Sc_Finish", SqlDbType.Bit,1),
					new SqlParameter("@Rk_Sl", SqlDbType.Int,4),
					new SqlParameter("@Yp_Code", SqlDbType.Char,11)};
			parameters[0].Value = model.Xl_Code;
			parameters[1].Value = model.Yp_Scph;
			parameters[2].Value = model.Yp_ScDate1;
			parameters[3].Value = model.Yp_ScDate2;
			parameters[4].Value = model.Yp_Memo;
			parameters[5].Value = model.Yp_Count;
			parameters[6].Value = model.Sc_Finish;
			parameters[7].Value = model.Rk_Sl;
			parameters[8].Value = model.Yp_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Yp_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_Yp3 ");
			strSql.Append(" where Yp_Code=@Yp_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yp_Code", SqlDbType.Char,11)         };
			parameters[0].Value = Yp_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Yp_Codelist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_Yp3 ");
			strSql.Append(" where Yp_Code in (" + Yp_Codelist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_Yp3 GetModel(string Yp_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Xl_Code,Yp_Code,Yp_Scph,Yp_ScDate1,Yp_ScDate2,Yp_Memo,Yp_Count,Sc_Finish,Rk_Sl from Zd_Yp3 ");
			strSql.Append(" where Yp_Code=@Yp_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yp_Code", SqlDbType.Char,11)         };
			parameters[0].Value = Yp_Code;

			Model.MdlZd_Yp3 model = new Model.MdlZd_Yp3();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_Yp3 DataRowToModel(DataRow row)
		{
			Model.MdlZd_Yp3 model = new Model.MdlZd_Yp3();
			if (row != null)
			{
				if (row["Xl_Code"] != null)
				{
					model.Xl_Code = row["Xl_Code"].ToString();
				}
				if (row["Yp_Code"] != null)
				{
					model.Yp_Code = row["Yp_Code"].ToString();
				}
				if (row["Yp_Scph"] != null)
				{
					model.Yp_Scph = row["Yp_Scph"].ToString();
				}
				if (row["Yp_ScDate1"] != null && row["Yp_ScDate1"].ToString() != "")
				{
					model.Yp_ScDate1 = DateTime.Parse(row["Yp_ScDate1"].ToString());
				}
				if (row["Yp_ScDate2"] != null && row["Yp_ScDate2"].ToString() != "")
				{
					model.Yp_ScDate2 = DateTime.Parse(row["Yp_ScDate2"].ToString());
				}
				if (row["Yp_Memo"] != null)
				{
					model.Yp_Memo = row["Yp_Memo"].ToString();
				}
				if (row["Yp_Count"] != null && row["Yp_Count"].ToString() != "")
				{
					model.Yp_Count = int.Parse(row["Yp_Count"].ToString());
				}
				if (row["Sc_Finish"] != null && row["Sc_Finish"].ToString() != "")
				{
					if ((row["Sc_Finish"].ToString() == "1") || (row["Sc_Finish"].ToString().ToLower() == "true"))
					{
						model.Sc_Finish = true;
					}
					else
					{
						model.Sc_Finish = false;
					}
				}
				if (row["Rk_Sl"] != null && row["Rk_Sl"].ToString() != "")
				{
					model.Rk_Sl = int.Parse(row["Rk_Sl"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Xl_Code,Yp_Code,Yp_Scph,Yp_ScDate1,Yp_ScDate2,Yp_Memo,Yp_Count,Sc_Finish,Rk_Sl ");
			strSql.Append(" FROM Zd_Yp3 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Xl_Code,Yp_Code,Yp_Scph,Yp_ScDate1,Yp_ScDate2,Yp_Memo,Yp_Count,Sc_Finish,Rk_Sl ");
			strSql.Append(" FROM Zd_Yp3 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Zd_Yp3 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Yp_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_Yp3 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_Yp3";
			parameters[1].Value = "Yp_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 获取药品批号与药品信息关联的数据列表
		/// </summary>
		/// <param name="strWhere">查询条件</param>
		/// <returns>关联查询结果</returns>
		public DataSet GetListWithYpInfo(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT ");
			strSql.Append("y2.*, ");
			strSql.Append("y3.Yp_Code, ");
			strSql.Append("y3.Xl_Code, ");
			strSql.Append("y3.Yp_Scph, ");
			strSql.Append("y3.Yp_ScDate1, ");
			strSql.Append("y3.Yp_ScDate2 ");
			strSql.Append("FROM Zd_Yp3 y3 ");
			strSql.Append("INNER JOIN Zd_Yp2 y2 ON y3.Xl_Code = y2.Xl_Code ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ORDER BY y2.Yp_Name, y3.Yp_Scph");
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		#endregion  ExtensionMethod
	}
}

