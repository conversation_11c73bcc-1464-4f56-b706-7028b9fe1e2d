﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlYp_Bhg.cs
*
* 功 能： N/A
* 类 名： MdlYp_Bhg
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:16   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// MdlYp_Bhg:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class MdlYp_Bhg
	{
		public MdlYp_Bhg()
		{ }
		#region Model
		private string _bhg_code;
		private DateTime? _bhg_date;
		private string _yp_pzwh;
		private string _yp_scph;
		private string _yp_name;
		private string _yp_jc;
		private string _yp_jx;
		private string _yp_zjgg;
		private string _yp_bzgg;
		private string _yp_scqy;
		private string _bhg_memo;
		private string _yp_code;
		/// <summary>
		/// 
		/// </summary>
		public string Bhg_Code
		{
			set { _bhg_code = value; }
			get { return _bhg_code; }
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Bhg_Date
		{
			set { _bhg_date = value; }
			get { return _bhg_date; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Pzwh
		{
			set { _yp_pzwh = value; }
			get { return _yp_pzwh; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Scph
		{
			set { _yp_scph = value; }
			get { return _yp_scph; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Name
		{
			set { _yp_name = value; }
			get { return _yp_name; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Jc
		{
			set { _yp_jc = value; }
			get { return _yp_jc; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Jx
		{
			set { _yp_jx = value; }
			get { return _yp_jx; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Zjgg
		{
			set { _yp_zjgg = value; }
			get { return _yp_zjgg; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Bzgg
		{
			set { _yp_bzgg = value; }
			get { return _yp_bzgg; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Scqy
		{
			set { _yp_scqy = value; }
			get { return _yp_scqy; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Bhg_Memo
		{
			set { _bhg_memo = value; }
			get { return _bhg_memo; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yp_Code
		{
			set { _yp_code = value; }
			get { return _yp_code; }
		}
		#endregion Model

	}
}

