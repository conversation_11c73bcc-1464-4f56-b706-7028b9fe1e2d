﻿/**  版本信息模板在安装目录下，可自行修改。
* DalYp_Bhg.cs
*
* 功 能： N/A
* 类 名： DalYp_Bhg
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:16   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalYp_Bhg
	/// </summary>
	public partial class DalYp_Bhg : IDalYp_Bhg
	{
		public DalYp_Bhg()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Bhg_Code)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from Yp_Bhg");
			strSql.Append(" where Bhg_Code=@Bhg_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bhg_Code", SqlDbType.Char,9)         };
			parameters[0].Value = Bhg_Code;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlYp_Bhg model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into Yp_Bhg(");
			strSql.Append("Bhg_Code,Bhg_Date,Yp_Pzwh,Yp_Scph,Yp_Name,Yp_Jc,Yp_Jx,Yp_Zjgg,Yp_Bzgg,Yp_Scqy,Bhg_Memo,Yp_Code)");
			strSql.Append(" values (");
			strSql.Append("@Bhg_Code,@Bhg_Date,@Yp_Pzwh,@Yp_Scph,@Yp_Name,@Yp_Jc,@Yp_Jx,@Yp_Zjgg,@Yp_Bzgg,@Yp_Scqy,@Bhg_Memo,@Yp_Code)");
			SqlParameter[] parameters = {
					new SqlParameter("@Bhg_Code", SqlDbType.Char,9),
					new SqlParameter("@Bhg_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Yp_Pzwh", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Scph", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Name", SqlDbType.VarChar,100),
					new SqlParameter("@Yp_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Jx", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Zjgg", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Bzgg", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Scqy", SqlDbType.VarChar,200),
					new SqlParameter("@Bhg_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Yp_Code", SqlDbType.Char,11)};
			parameters[0].Value = model.Bhg_Code;
			parameters[1].Value = model.Bhg_Date;
			parameters[2].Value = model.Yp_Pzwh;
			parameters[3].Value = model.Yp_Scph;
			parameters[4].Value = model.Yp_Name;
			parameters[5].Value = model.Yp_Jc;
			parameters[6].Value = model.Yp_Jx;
			parameters[7].Value = model.Yp_Zjgg;
			parameters[8].Value = model.Yp_Bzgg;
			parameters[9].Value = model.Yp_Scqy;
			parameters[10].Value = model.Bhg_Memo;
			parameters[11].Value = Common.Tools.IsValueNull(model.Yp_Code);
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlYp_Bhg model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Yp_Bhg set ");
			strSql.Append("Bhg_Date=@Bhg_Date,");
			strSql.Append("Yp_Pzwh=@Yp_Pzwh,");
			strSql.Append("Yp_Scph=@Yp_Scph,");
			strSql.Append("Yp_Name=@Yp_Name,");
			strSql.Append("Yp_Jc=@Yp_Jc,");
			strSql.Append("Yp_Jx=@Yp_Jx,");
			strSql.Append("Yp_Zjgg=@Yp_Zjgg,");
			strSql.Append("Yp_Bzgg=@Yp_Bzgg,");
			strSql.Append("Yp_Scqy=@Yp_Scqy,");
			strSql.Append("Bhg_Memo=@Bhg_Memo,");
			strSql.Append("Yp_Code=@Yp_Code");
			strSql.Append(" where Bhg_Code=@Bhg_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bhg_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Yp_Pzwh", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Scph", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Name", SqlDbType.VarChar,100),
					new SqlParameter("@Yp_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Jx", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Zjgg", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Bzgg", SqlDbType.VarChar,50),
					new SqlParameter("@Yp_Scqy", SqlDbType.VarChar,200),
					new SqlParameter("@Bhg_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Bhg_Code", SqlDbType.Char,9),
					new SqlParameter("@Yp_Code", SqlDbType.Char,11)};
			parameters[0].Value = model.Bhg_Date;
			parameters[1].Value = model.Yp_Pzwh;
			parameters[2].Value = model.Yp_Scph;
			parameters[3].Value = model.Yp_Name;
			parameters[4].Value = model.Yp_Jc;
			parameters[5].Value = model.Yp_Jx;
			parameters[6].Value = model.Yp_Zjgg;
			parameters[7].Value = model.Yp_Bzgg;
			parameters[8].Value = model.Yp_Scqy;
			parameters[9].Value = model.Bhg_Memo;
			parameters[10].Value = model.Bhg_Code;
			parameters[11].Value = Common.Tools.IsValueNull(model.Yp_Code);
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Bhg_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Yp_Bhg ");
			strSql.Append(" where Bhg_Code=@Bhg_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bhg_Code", SqlDbType.Char,9)         };
			parameters[0].Value = Bhg_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Bhg_Codelist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Yp_Bhg ");
			strSql.Append(" where Bhg_Code in (" + Bhg_Codelist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlYp_Bhg GetModel(string Bhg_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Bhg_Code,Bhg_Date,Yp_Pzwh,Yp_Scph,Yp_Name,Yp_Jc,Yp_Jx,Yp_Zjgg,Yp_Bzgg,Yp_Scqy,Bhg_Memo,Yp_Code from Yp_Bhg ");
			strSql.Append(" where Bhg_Code=@Bhg_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bhg_Code", SqlDbType.Char,9)         };
			parameters[0].Value = Bhg_Code;

			Model.MdlYp_Bhg model = new Model.MdlYp_Bhg();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlYp_Bhg DataRowToModel(DataRow row)
		{
			Model.MdlYp_Bhg model = new Model.MdlYp_Bhg();
			if (row != null)
			{
				if (row["Bhg_Code"] != null)
				{
					model.Bhg_Code = row["Bhg_Code"].ToString();
				}
				if (row["Bhg_Date"] != null && row["Bhg_Date"].ToString() != "")
				{
					model.Bhg_Date = DateTime.Parse(row["Bhg_Date"].ToString());
				}
				if (row["Yp_Pzwh"] != null)
				{
					model.Yp_Pzwh = row["Yp_Pzwh"].ToString();
				}
				if (row["Yp_Scph"] != null)
				{
					model.Yp_Scph = row["Yp_Scph"].ToString();
				}
				if (row["Yp_Name"] != null)
				{
					model.Yp_Name = row["Yp_Name"].ToString();
				}
				if (row["Yp_Jc"] != null)
				{
					model.Yp_Jc = row["Yp_Jc"].ToString();
				}
				if (row["Yp_Jx"] != null)
				{
					model.Yp_Jx = row["Yp_Jx"].ToString();
				}
				if (row["Yp_Zjgg"] != null)
				{
					model.Yp_Zjgg = row["Yp_Zjgg"].ToString();
				}
				if (row["Yp_Bzgg"] != null)
				{
					model.Yp_Bzgg = row["Yp_Bzgg"].ToString();
				}
				if (row["Yp_Scqy"] != null)
				{
					model.Yp_Scqy = row["Yp_Scqy"].ToString();
				}
				if (row["Bhg_Memo"] != null)
				{
					model.Bhg_Memo = row["Bhg_Memo"].ToString();
				}
				if (row["Yp_Code"] != null)
				{
					model.Yp_Code = row["Yp_Code"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Bhg_Code,Bhg_Date,Yp_Pzwh,Yp_Scph,Yp_Name,Yp_Jc,Yp_Jx,Yp_Zjgg,Yp_Bzgg,Yp_Scqy,Bhg_Memo,Yp_Code ");
			strSql.Append(" FROM Yp_Bhg ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Bhg_Code,Bhg_Date,Yp_Pzwh,Yp_Scph,Yp_Name,Yp_Jc,Yp_Jx,Yp_Zjgg,Yp_Bzgg,Yp_Scqy,Bhg_Memo,Yp_Code ");
			strSql.Append(" FROM Yp_Bhg ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Yp_Bhg ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Bhg_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Yp_Bhg T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Yp_Bhg";
			parameters[1].Value = "Bhg_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 得到最大编码
		/// </summary
		public string MaxCode(int length)
		{
			string max = "BHG" + Common.WinFormVar.Var.DbHelper.F_MaxCode($"SELECT MAX(RIGHT(Bhg_Code,6)) FROM Yp_Bhg WHERE LEFT(Bhg_Code,3)='BHG'", length - 3);
			return max;
		}

		#endregion  ExtensionMethod
	}
}

