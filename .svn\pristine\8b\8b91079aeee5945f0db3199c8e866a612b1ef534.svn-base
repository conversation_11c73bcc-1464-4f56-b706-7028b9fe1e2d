﻿/**  版本信息模板在安装目录下，可自行修改。
* DalZd_PxJl.cs
*
* 功 能： N/A
* 类 名： DalZd_PxJl
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:19   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalZd_PxJl
	/// </summary>
	public partial class DalZd_PxJl : IDalZd_PxJl
	{
		public DalZd_PxJl()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Px2_Code)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from Zd_PxJl");
			strSql.Append(" where Px2_Code=@Px2_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Px2_Code", SqlDbType.Char,10)            };
			parameters[0].Value = Px2_Code;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlZd_PxJl model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into Zd_PxJl(");
			strSql.Append("Px1_Code,Px2_Code,Ry_Code,Ry_Name,Px2_Date,Px2_KhJg,Px2_Bx,Px2_Cj,Px2_Pdr,Px2_Memo,Lr_Date,Jsr_Code,Jsr_Name)");
			strSql.Append(" values (");
			strSql.Append("@Px1_Code,@Px2_Code,@Ry_Code,@Ry_Name,@Px2_Date,@Px2_KhJg,@Px2_Bx,@Px2_Cj,@Px2_Pdr,@Px2_Memo,@Lr_Date,@Jsr_Code,@Jsr_Name)");
			SqlParameter[] parameters = {
					new SqlParameter("@Px1_Code", SqlDbType.Char,10),
					new SqlParameter("@Px2_Code", SqlDbType.Char,10),
					new SqlParameter("@Ry_Code", SqlDbType.Char,7),
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Px2_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Px2_KhJg", SqlDbType.VarChar,50),
					new SqlParameter("@Px2_Bx", SqlDbType.VarChar,100),
					new SqlParameter("@Px2_Cj", SqlDbType.VarChar,50),
					new SqlParameter("@Px2_Pdr", SqlDbType.VarChar,50),
					new SqlParameter("@Px2_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Lr_Date", SqlDbType.DateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Jsr_Name", SqlDbType.VarChar,50)};
			parameters[0].Value = model.Px1_Code;
			parameters[1].Value = model.Px2_Code;
			parameters[2].Value = model.Ry_Code;
			parameters[3].Value = model.Ry_Name;
			parameters[4].Value = model.Px2_Date;
			parameters[5].Value = model.Px2_KhJg;
			parameters[6].Value = model.Px2_Bx;
			parameters[7].Value = model.Px2_Cj;
			parameters[8].Value = model.Px2_Pdr;
			parameters[9].Value = model.Px2_Memo;
			parameters[10].Value = model.Lr_Date;
			parameters[11].Value = model.Jsr_Code;
			parameters[12].Value = model.Jsr_Name;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlZd_PxJl model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Zd_PxJl set ");
			strSql.Append("Px1_Code=@Px1_Code,");
			strSql.Append("Ry_Code=@Ry_Code,");
			strSql.Append("Ry_Name=@Ry_Name,");
			strSql.Append("Px2_Date=@Px2_Date,");
			strSql.Append("Px2_KhJg=@Px2_KhJg,");
			strSql.Append("Px2_Bx=@Px2_Bx,");
			strSql.Append("Px2_Cj=@Px2_Cj,");
			strSql.Append("Px2_Pdr=@Px2_Pdr,");
			strSql.Append("Px2_Memo=@Px2_Memo,");
			strSql.Append("Lr_Date=@Lr_Date,");
			strSql.Append("Jsr_Code=@Jsr_Code,");
			strSql.Append("Jsr_Name=@Jsr_Name");
			strSql.Append(" where Px2_Code=@Px2_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Px1_Code", SqlDbType.Char,10),
					new SqlParameter("@Ry_Code", SqlDbType.Char,7),
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Px2_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Px2_KhJg", SqlDbType.VarChar,50),
					new SqlParameter("@Px2_Bx", SqlDbType.VarChar,100),
					new SqlParameter("@Px2_Cj", SqlDbType.VarChar,50),
					new SqlParameter("@Px2_Pdr", SqlDbType.VarChar,50),
					new SqlParameter("@Px2_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Lr_Date", SqlDbType.DateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Jsr_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Px2_Code", SqlDbType.Char,10)};
			parameters[0].Value = model.Px1_Code;
			parameters[1].Value = model.Ry_Code;
			parameters[2].Value = model.Ry_Name;
			parameters[3].Value = model.Px2_Date;
			parameters[4].Value = model.Px2_KhJg;
			parameters[5].Value = model.Px2_Bx;
			parameters[6].Value = model.Px2_Cj;
			parameters[7].Value = model.Px2_Pdr;
			parameters[8].Value = model.Px2_Memo;
			parameters[9].Value = model.Lr_Date;
			parameters[10].Value = model.Jsr_Code;
			parameters[11].Value = model.Jsr_Name;
			parameters[12].Value = model.Px2_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Px2_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_PxJl ");
			strSql.Append(" where Px2_Code=@Px2_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Px2_Code", SqlDbType.Char,10)            };
			parameters[0].Value = Px2_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Px2_Codelist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_PxJl ");
			strSql.Append(" where Px2_Code in (" + Px2_Codelist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_PxJl GetModel(string Px2_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Px1_Code,Px2_Code,Ry_Code,Ry_Name,Px2_Date,Px2_KhJg,Px2_Bx,Px2_Cj,Px2_Pdr,Px2_Memo,Lr_Date,Jsr_Code,Jsr_Name from Zd_PxJl ");
			strSql.Append(" where Px2_Code=@Px2_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Px2_Code", SqlDbType.Char,10)            };
			parameters[0].Value = Px2_Code;

			Model.MdlZd_PxJl model = new Model.MdlZd_PxJl();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_PxJl DataRowToModel(DataRow row)
		{
			Model.MdlZd_PxJl model = new Model.MdlZd_PxJl();
			if (row != null)
			{
				if (row["Px1_Code"] != null)
				{
					model.Px1_Code = row["Px1_Code"].ToString();
				}
				if (row["Px2_Code"] != null)
				{
					model.Px2_Code = row["Px2_Code"].ToString();
				}
				if (row["Ry_Code"] != null)
				{
					model.Ry_Code = row["Ry_Code"].ToString();
				}
				if (row["Ry_Name"] != null)
				{
					model.Ry_Name = row["Ry_Name"].ToString();
				}
				if (row["Px2_Date"] != null && row["Px2_Date"].ToString() != "")
				{
					model.Px2_Date = DateTime.Parse(row["Px2_Date"].ToString());
				}
				if (row["Px2_KhJg"] != null)
				{
					model.Px2_KhJg = row["Px2_KhJg"].ToString();
				}
				if (row["Px2_Bx"] != null)
				{
					model.Px2_Bx = row["Px2_Bx"].ToString();
				}
				if (row["Px2_Cj"] != null)
				{
					model.Px2_Cj = row["Px2_Cj"].ToString();
				}
				if (row["Px2_Pdr"] != null)
				{
					model.Px2_Pdr = row["Px2_Pdr"].ToString();
				}
				if (row["Px2_Memo"] != null)
				{
					model.Px2_Memo = row["Px2_Memo"].ToString();
				}
				if (row["Lr_Date"] != null && row["Lr_Date"].ToString() != "")
				{
					model.Lr_Date = DateTime.Parse(row["Lr_Date"].ToString());
				}
				if (row["Jsr_Code"] != null)
				{
					model.Jsr_Code = row["Jsr_Code"].ToString();
				}
				if (row["Jsr_Name"] != null)
				{
					model.Jsr_Name = row["Jsr_Name"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Px1_Code,Px2_Code,Ry_Code,Ry_Name,Px2_Date,Px2_KhJg,Px2_Bx,Px2_Cj,Px2_Pdr,Px2_Memo,Lr_Date,Jsr_Code,Jsr_Name ");
			strSql.Append(" FROM Zd_PxJl ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Px1_Code,Px2_Code,Ry_Code,Ry_Name,Px2_Date,Px2_KhJg,Px2_Bx,Px2_Cj,Px2_Pdr,Px2_Memo,Lr_Date,Jsr_Code,Jsr_Name ");
			strSql.Append(" FROM Zd_PxJl ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Zd_PxJl ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Px2_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_PxJl T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_PxJl";
			parameters[1].Value = "Px2_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = (string)(Common.WinFormVar.Var.DbHelper.F_MaxCode("SELECT MAX(Px2_Code) FROM Zd_PxJl where LEN(Px2_Code)=" + length, length));
			return max;
		}

		#endregion  ExtensionMethod
	}
}

