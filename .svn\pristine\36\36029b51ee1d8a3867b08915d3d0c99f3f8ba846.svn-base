﻿/**  版本信息模板在安装目录下，可自行修改。
* DalZd_Blfy.cs
*
* 功 能： N/A
* 类 名： DalZd_Blfy
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:16   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalZd_Blfy
	/// </summary>
	public partial class DalZd_Blfy : IDalZd_Blfy
	{
		public DalZd_Blfy()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Blfy_Code)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from Zd_Blfy");
			strSql.Append(" where Blfy_Code=@Blfy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Blfy_Code", SqlDbType.Char,4)            };
			parameters[0].Value = Blfy_Code;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlZd_Blfy model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into Zd_Blfy(");
			strSql.Append("Blfy_Code,Ry_Name,Ry_Sex,Ry_Sfzh,Ry_Mz,Ry_Tele,Ry_Birth,Ry_Blh,Ry_Address,Blfy_Jg,Ry_Jwywblfy,Blfy_Time,Blfy_Mc,Ry_Yhjb,Blfy_Yx,Ry_Jzywblfy,Blfy_Dzsw,Blfy_IsSw,Blfy_Zjsw,Blfy_SwTime,Blfy_Sjpj,Blfy_Gjpj,Blfy_Gnfy,Glfy_Gwfy,Blfy_Lcbx,Blfy_Clqk,Blfy_Memo)");
			strSql.Append(" values (");
			strSql.Append("@Blfy_Code,@Ry_Name,@Ry_Sex,@Ry_Sfzh,@Ry_Mz,@Ry_Tele,@Ry_Birth,@Ry_Blh,@Ry_Address,@Blfy_Jg,@Ry_Jwywblfy,@Blfy_Time,@Blfy_Mc,@Ry_Yhjb,@Blfy_Yx,@Ry_Jzywblfy,@Blfy_Dzsw,@Blfy_IsSw,@Blfy_Zjsw,@Blfy_SwTime,@Blfy_Sjpj,@Blfy_Gjpj,@Blfy_Gnfy,@Glfy_Gwfy,@Blfy_Lcbx,@Blfy_Clqk,@Blfy_Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@Blfy_Code", SqlDbType.Char,4),
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Sex", SqlDbType.Char,2),
					new SqlParameter("@Ry_Sfzh", SqlDbType.VarChar,18),
					new SqlParameter("@Ry_Mz", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Tele", SqlDbType.VarChar,11),
					new SqlParameter("@Ry_Birth", SqlDbType.SmallDateTime),
					new SqlParameter("@Ry_Blh", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Address", SqlDbType.VarChar,100),
					new SqlParameter("@Blfy_Jg", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Jwywblfy", SqlDbType.VarChar,50),
					new SqlParameter("@Blfy_Time", SqlDbType.SmallDateTime),
					new SqlParameter("@Blfy_Mc", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Yhjb", SqlDbType.VarChar,100),
					new SqlParameter("@Blfy_Yx", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Jzywblfy", SqlDbType.VarChar,50),
					new SqlParameter("@Blfy_Dzsw", SqlDbType.VarChar,50),
					new SqlParameter("@Blfy_IsSw", SqlDbType.VarChar,10),
					new SqlParameter("@Blfy_Zjsw", SqlDbType.VarChar,50),
					new SqlParameter("@Blfy_SwTime", SqlDbType.SmallDateTime),
					new SqlParameter("@Blfy_Sjpj", SqlDbType.VarChar,50),
					new SqlParameter("@Blfy_Gjpj", SqlDbType.VarChar,50),
					new SqlParameter("@Blfy_Gnfy", SqlDbType.VarChar,50),
					new SqlParameter("@Glfy_Gwfy", SqlDbType.VarChar,50),
					new SqlParameter("@Blfy_Lcbx", SqlDbType.VarChar,500),
					new SqlParameter("@Blfy_Clqk", SqlDbType.VarChar,500),
					new SqlParameter("@Blfy_Memo", SqlDbType.VarChar,500)};
			parameters[0].Value = model.Blfy_Code;
			parameters[1].Value = model.Ry_Name;
			parameters[2].Value = model.Ry_Sex;
			parameters[3].Value = model.Ry_Sfzh;
			parameters[4].Value = model.Ry_Mz;
			parameters[5].Value = model.Ry_Tele;
			parameters[6].Value = Common.Tools.IsValueNull(model.Ry_Birth);
			parameters[7].Value = model.Ry_Blh;
			parameters[8].Value = model.Ry_Address;
			parameters[9].Value = model.Blfy_Jg;
			parameters[10].Value = model.Ry_Jwywblfy;
			parameters[11].Value = Common.Tools.IsValueNull(model.Blfy_Time);
			parameters[12].Value = model.Blfy_Mc;
			parameters[13].Value = model.Ry_Yhjb;
			parameters[14].Value = model.Blfy_Yx;
			parameters[15].Value = model.Ry_Jzywblfy;
			parameters[16].Value = model.Blfy_Dzsw;
			parameters[17].Value = model.Blfy_IsSw;
			parameters[18].Value = model.Blfy_Zjsw;
			parameters[19].Value = Common.Tools.IsValueNull(model.Blfy_SwTime);
			parameters[20].Value = model.Blfy_Sjpj;
			parameters[21].Value = model.Blfy_Gjpj;
			parameters[22].Value = model.Blfy_Gnfy;
			parameters[23].Value = model.Glfy_Gwfy;
			parameters[24].Value = model.Blfy_Lcbx;
			parameters[25].Value = model.Blfy_Clqk;
			parameters[26].Value = model.Blfy_Memo;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlZd_Blfy model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Zd_Blfy set ");
			strSql.Append("Ry_Name=@Ry_Name,");
			strSql.Append("Ry_Sex=@Ry_Sex,");
			strSql.Append("Ry_Sfzh=@Ry_Sfzh,");
			strSql.Append("Ry_Mz=@Ry_Mz,");
			strSql.Append("Ry_Tele=@Ry_Tele,");
			strSql.Append("Ry_Birth=@Ry_Birth,");
			strSql.Append("Ry_Blh=@Ry_Blh,");
			strSql.Append("Ry_Address=@Ry_Address,");
			strSql.Append("Blfy_Jg=@Blfy_Jg,");
			strSql.Append("Ry_Jwywblfy=@Ry_Jwywblfy,");
			strSql.Append("Blfy_Time=@Blfy_Time,");
			strSql.Append("Blfy_Mc=@Blfy_Mc,");
			strSql.Append("Ry_Yhjb=@Ry_Yhjb,");
			strSql.Append("Blfy_Yx=@Blfy_Yx,");
			strSql.Append("Ry_Jzywblfy=@Ry_Jzywblfy,");
			strSql.Append("Blfy_Dzsw=@Blfy_Dzsw,");
			strSql.Append("Blfy_IsSw=@Blfy_IsSw,");
			strSql.Append("Blfy_Zjsw=@Blfy_Zjsw,");
			strSql.Append("Blfy_SwTime=@Blfy_SwTime,");
			strSql.Append("Blfy_Sjpj=@Blfy_Sjpj,");
			strSql.Append("Blfy_Gjpj=@Blfy_Gjpj,");
			strSql.Append("Blfy_Gnfy=@Blfy_Gnfy,");
			strSql.Append("Glfy_Gwfy=@Glfy_Gwfy,");
			strSql.Append("Blfy_Lcbx=@Blfy_Lcbx,");
			strSql.Append("Blfy_Clqk=@Blfy_Clqk,");
			strSql.Append("Blfy_Memo=@Blfy_Memo");
			strSql.Append(" where Blfy_Code=@Blfy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Sex", SqlDbType.Char,2),
					new SqlParameter("@Ry_Sfzh", SqlDbType.VarChar,18),
					new SqlParameter("@Ry_Mz", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Tele", SqlDbType.VarChar,11),
					new SqlParameter("@Ry_Birth", SqlDbType.SmallDateTime),
					new SqlParameter("@Ry_Blh", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Address", SqlDbType.VarChar,100),
					new SqlParameter("@Blfy_Jg", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Jwywblfy", SqlDbType.VarChar,50),
					new SqlParameter("@Blfy_Time", SqlDbType.SmallDateTime),
					new SqlParameter("@Blfy_Mc", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Yhjb", SqlDbType.VarChar,100),
					new SqlParameter("@Blfy_Yx", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Jzywblfy", SqlDbType.VarChar,50),
					new SqlParameter("@Blfy_Dzsw", SqlDbType.VarChar,50),
					new SqlParameter("@Blfy_IsSw", SqlDbType.VarChar,10),
					new SqlParameter("@Blfy_Zjsw", SqlDbType.VarChar,50),
					new SqlParameter("@Blfy_SwTime", SqlDbType.SmallDateTime),
					new SqlParameter("@Blfy_Sjpj", SqlDbType.VarChar,50),
					new SqlParameter("@Blfy_Gjpj", SqlDbType.VarChar,50),
					new SqlParameter("@Blfy_Gnfy", SqlDbType.VarChar,50),
					new SqlParameter("@Glfy_Gwfy", SqlDbType.VarChar,50),
					new SqlParameter("@Blfy_Lcbx", SqlDbType.VarChar,500),
					new SqlParameter("@Blfy_Clqk", SqlDbType.VarChar,500),
					new SqlParameter("@Blfy_Memo", SqlDbType.VarChar,500),
					new SqlParameter("@Blfy_Code", SqlDbType.Char,4)};
			parameters[0].Value = model.Ry_Name;
			parameters[1].Value = model.Ry_Sex;
			parameters[2].Value = model.Ry_Sfzh;
			parameters[3].Value = model.Ry_Mz;
			parameters[4].Value = model.Ry_Tele;
			parameters[5].Value = Common.Tools.IsValueNull(model.Ry_Birth);
			parameters[6].Value = model.Ry_Blh;
			parameters[7].Value = model.Ry_Address;
			parameters[8].Value = model.Blfy_Jg;
			parameters[9].Value = model.Ry_Jwywblfy;
			parameters[10].Value = Common.Tools.IsValueNull(model.Blfy_Time);
			parameters[11].Value = model.Blfy_Mc;
			parameters[12].Value = model.Ry_Yhjb;
			parameters[13].Value = model.Blfy_Yx;
			parameters[14].Value = model.Ry_Jzywblfy;
			parameters[15].Value = model.Blfy_Dzsw;
			parameters[16].Value = model.Blfy_IsSw;
			parameters[17].Value = model.Blfy_Zjsw;
			parameters[18].Value = Common.Tools.IsValueNull(model.Blfy_SwTime);
			parameters[19].Value = model.Blfy_Sjpj;
			parameters[20].Value = model.Blfy_Gjpj;
			parameters[21].Value = model.Blfy_Gnfy;
			parameters[22].Value = model.Glfy_Gwfy;
			parameters[23].Value = model.Blfy_Lcbx;
			parameters[24].Value = model.Blfy_Clqk;
			parameters[25].Value = model.Blfy_Memo;
			parameters[26].Value = model.Blfy_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Blfy_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_Blfy ");
			strSql.Append(" where Blfy_Code=@Blfy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Blfy_Code", SqlDbType.Char,4)            };
			parameters[0].Value = Blfy_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Blfy_Codelist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_Blfy ");
			strSql.Append(" where Blfy_Code in (" + Blfy_Codelist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_Blfy GetModel(string Blfy_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Blfy_Code,Ry_Name,Ry_Sex,Ry_Sfzh,Ry_Mz,Ry_Tele,Ry_Birth,Ry_Blh,Ry_Address,Blfy_Jg,Ry_Jwywblfy,Blfy_Time,Blfy_Mc,Ry_Yhjb,Blfy_Yx,Ry_Jzywblfy,Blfy_Dzsw,Blfy_IsSw,Blfy_Zjsw,Blfy_SwTime,Blfy_Sjpj,Blfy_Gjpj,Blfy_Gnfy,Glfy_Gwfy,Blfy_Lcbx,Blfy_Clqk,Blfy_Memo from Zd_Blfy ");
			strSql.Append(" where Blfy_Code=@Blfy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Blfy_Code", SqlDbType.Char,4)            };
			parameters[0].Value = Blfy_Code;

			Model.MdlZd_Blfy model = new Model.MdlZd_Blfy();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_Blfy DataRowToModel(DataRow row)
		{
			Model.MdlZd_Blfy model = new Model.MdlZd_Blfy();
			if (row != null)
			{
				if (row["Blfy_Code"] != null)
				{
					model.Blfy_Code = row["Blfy_Code"].ToString();
				}
				if (row["Ry_Name"] != null)
				{
					model.Ry_Name = row["Ry_Name"].ToString();
				}
				if (row["Ry_Sex"] != null)
				{
					model.Ry_Sex = row["Ry_Sex"].ToString();
				}
				if (row["Ry_Sfzh"] != null)
				{
					model.Ry_Sfzh = row["Ry_Sfzh"].ToString();
				}
				if (row["Ry_Mz"] != null)
				{
					model.Ry_Mz = row["Ry_Mz"].ToString();
				}
				if (row["Ry_Tele"] != null)
				{
					model.Ry_Tele = row["Ry_Tele"].ToString();
				}
				if (row["Ry_Birth"] != null && row["Ry_Birth"].ToString() != "")
				{
					model.Ry_Birth = DateTime.Parse(row["Ry_Birth"].ToString());
				}
				if (row["Ry_Blh"] != null)
				{
					model.Ry_Blh = row["Ry_Blh"].ToString();
				}
				if (row["Ry_Address"] != null)
				{
					model.Ry_Address = row["Ry_Address"].ToString();
				}
				if (row["Blfy_Jg"] != null)
				{
					model.Blfy_Jg = row["Blfy_Jg"].ToString();
				}
				if (row["Ry_Jwywblfy"] != null)
				{
					model.Ry_Jwywblfy = row["Ry_Jwywblfy"].ToString();
				}
				if (row["Blfy_Time"] != null && row["Blfy_Time"].ToString() != "")
				{
					model.Blfy_Time = DateTime.Parse(row["Blfy_Time"].ToString());
				}
				if (row["Blfy_Mc"] != null)
				{
					model.Blfy_Mc = row["Blfy_Mc"].ToString();
				}
				if (row["Ry_Yhjb"] != null)
				{
					model.Ry_Yhjb = row["Ry_Yhjb"].ToString();
				}
				if (row["Blfy_Yx"] != null)
				{
					model.Blfy_Yx = row["Blfy_Yx"].ToString();
				}
				if (row["Ry_Jzywblfy"] != null)
				{
					model.Ry_Jzywblfy = row["Ry_Jzywblfy"].ToString();
				}
				if (row["Blfy_Dzsw"] != null)
				{
					model.Blfy_Dzsw = row["Blfy_Dzsw"].ToString();
				}
				if (row["Blfy_IsSw"] != null)
				{
					model.Blfy_IsSw = row["Blfy_IsSw"].ToString();
				}
				if (row["Blfy_Zjsw"] != null)
				{
					model.Blfy_Zjsw = row["Blfy_Zjsw"].ToString();
				}
				if (row["Blfy_SwTime"] != null && row["Blfy_SwTime"].ToString() != "")
				{
					model.Blfy_SwTime = DateTime.Parse(row["Blfy_SwTime"].ToString());
				}
				if (row["Blfy_Sjpj"] != null)
				{
					model.Blfy_Sjpj = row["Blfy_Sjpj"].ToString();
				}
				if (row["Blfy_Gjpj"] != null)
				{
					model.Blfy_Gjpj = row["Blfy_Gjpj"].ToString();
				}
				if (row["Blfy_Gnfy"] != null)
				{
					model.Blfy_Gnfy = row["Blfy_Gnfy"].ToString();
				}
				if (row["Glfy_Gwfy"] != null)
				{
					model.Glfy_Gwfy = row["Glfy_Gwfy"].ToString();
				}
				if (row["Blfy_Lcbx"] != null)
				{
					model.Blfy_Lcbx = row["Blfy_Lcbx"].ToString();
				}
				if (row["Blfy_Clqk"] != null)
				{
					model.Blfy_Clqk = row["Blfy_Clqk"].ToString();
				}
				if (row["Blfy_Memo"] != null)
				{
					model.Blfy_Memo = row["Blfy_Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Blfy_Code,Ry_Name,Ry_Sex,Ry_Sfzh,Ry_Mz,Ry_Tele,Ry_Birth,Ry_Blh,Ry_Address,Blfy_Jg,Ry_Jwywblfy,Blfy_Time,Blfy_Mc,Ry_Yhjb,Blfy_Yx,Ry_Jzywblfy,Blfy_Dzsw,Blfy_IsSw,Blfy_Zjsw,Blfy_SwTime,Blfy_Sjpj,Blfy_Gjpj,Blfy_Gnfy,Glfy_Gwfy,Blfy_Lcbx,Blfy_Clqk,Blfy_Memo ");
			strSql.Append(" FROM Zd_Blfy ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Blfy_Code,Ry_Name,Ry_Sex,Ry_Sfzh,Ry_Mz,Ry_Tele,Ry_Birth,Ry_Blh,Ry_Address,Blfy_Jg,Ry_Jwywblfy,Blfy_Time,Blfy_Mc,Ry_Yhjb,Blfy_Yx,Ry_Jzywblfy,Blfy_Dzsw,Blfy_IsSw,Blfy_Zjsw,Blfy_SwTime,Blfy_Sjpj,Blfy_Gjpj,Blfy_Gnfy,Glfy_Gwfy,Blfy_Lcbx,Blfy_Clqk,Blfy_Memo ");
			strSql.Append(" FROM Zd_Blfy ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Zd_Blfy ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Blfy_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_Blfy T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_Blfy";
			parameters[1].Value = "Blfy_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = (string)(Common.WinFormVar.Var.DbHelper.F_MaxCode("SELECT MAX(Blfy_Code) FROM Zd_Blfy where LEN(Blfy_Code)=" + length, length));
			return max;
		}

		#endregion  ExtensionMethod
	}
}

