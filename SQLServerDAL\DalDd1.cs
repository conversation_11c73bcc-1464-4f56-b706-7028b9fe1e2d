﻿/**  版本信息模板在安装目录下，可自行修改。
* DalDd1.cs
*
* 功 能： N/A
* 类 名： DalDd1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:15   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalDd1
	/// </summary>
	public partial class DalDd1 : IDalDd1
	{
		public DalDd1()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Dd_Code)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from Dd1");
			strSql.Append(" where Dd_Code=@Dd_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Dd_Code", SqlDbType.Char,9)          };
			parameters[0].Value = Dd_Code;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlDd1 model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into Dd1(");
			strSql.Append("Dd_Date,Dd_Code,Kh_Code,Kh_Name,Dd_Money,Ys_Money,Dd_Memo,Dd_Finish,Cgy_Code,Cgy_Name,Ysy_Code,Ysy_Name,Ys_Date)");
			strSql.Append(" values (");
			strSql.Append("@Dd_Date,@Dd_Code,@Kh_Code,@Kh_Name,@Dd_Money,@Ys_Money,@Dd_Memo,@Dd_Finish,@Cgy_Code,@Cgy_Name,@Ysy_Code,@Ysy_Name,@Ys_Date)");
			SqlParameter[] parameters = {
					new SqlParameter("@Dd_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Dd_Code", SqlDbType.Char,9),
					new SqlParameter("@Kh_Code", SqlDbType.Char,4),
					new SqlParameter("@Kh_Name", SqlDbType.VarChar,100),
					new SqlParameter("@Dd_Money", SqlDbType.Decimal,9),
					new SqlParameter("@Ys_Money", SqlDbType.Decimal,9),
					new SqlParameter("@Dd_Memo", SqlDbType.VarChar,100),
					new SqlParameter("@Dd_Finish", SqlDbType.Char,1),
					new SqlParameter("@Cgy_Code", SqlDbType.Char,3),
					new SqlParameter("@Cgy_Name", SqlDbType.VarChar,10),
					new SqlParameter("@Ysy_Code", SqlDbType.Char,3),
					new SqlParameter("@Ysy_Name", SqlDbType.VarChar,10),
					new SqlParameter("@Ys_Date", SqlDbType.SmallDateTime)};
			parameters[0].Value = model.Dd_Date;
			parameters[1].Value = model.Dd_Code;
			parameters[2].Value = model.Kh_Code;
			parameters[3].Value = model.Kh_Name;
			parameters[4].Value = model.Dd_Money;
			parameters[5].Value = Common.Tools.IsValueNull(model.Ys_Money);
			parameters[6].Value = model.Dd_Memo;
			parameters[7].Value = model.Dd_Finish;
			parameters[8].Value = model.Cgy_Code;
			parameters[9].Value = model.Cgy_Name;
			parameters[10].Value = Common.Tools.IsValueNull(model.Ysy_Code);
			parameters[11].Value = Common.Tools.IsValueNull(model.Ysy_Name);
			parameters[12].Value = Common.Tools.IsValueNull(model.Ys_Date);

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlDd1 model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Dd1 set ");
			strSql.Append("Dd_Date=@Dd_Date,");
			strSql.Append("Kh_Code=@Kh_Code,");
			strSql.Append("Kh_Name=@Kh_Name,");
			strSql.Append("Dd_Money=@Dd_Money,");
			strSql.Append("Ys_Money=@Ys_Money,");
			strSql.Append("Dd_Memo=@Dd_Memo,");
			strSql.Append("Dd_Finish=@Dd_Finish,");
			strSql.Append("Cgy_Code=@Cgy_Code,");
			strSql.Append("Cgy_Name=@Cgy_Name,");
			strSql.Append("Ysy_Code=@Ysy_Code,");
			strSql.Append("Ysy_Name=@Ysy_Name,");
			strSql.Append("Ys_Date=@Ys_Date");
			strSql.Append(" where Dd_Code=@Dd_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Dd_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Kh_Code", SqlDbType.Char,4),
					new SqlParameter("@Kh_Name", SqlDbType.VarChar,100),
					new SqlParameter("@Dd_Money", SqlDbType.Decimal,9),
					new SqlParameter("@Ys_Money", SqlDbType.Decimal,9),
					new SqlParameter("@Dd_Memo", SqlDbType.VarChar,100),
					new SqlParameter("@Dd_Finish", SqlDbType.Char,1),
					new SqlParameter("@Cgy_Code", SqlDbType.Char,3),
					new SqlParameter("@Cgy_Name", SqlDbType.VarChar,10),
					new SqlParameter("@Ysy_Code", SqlDbType.Char,3),
					new SqlParameter("@Ysy_Name", SqlDbType.VarChar,10),
					new SqlParameter("@Ys_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Dd_Code", SqlDbType.Char,9)};
			parameters[0].Value = model.Dd_Date;
			parameters[1].Value = model.Kh_Code;
			parameters[2].Value = model.Kh_Name;
			parameters[3].Value = model.Dd_Money;
			parameters[4].Value = Common.Tools.IsValueNull(model.Ys_Money);
			parameters[5].Value = model.Dd_Memo;
			parameters[6].Value = model.Dd_Finish;
			parameters[7].Value = model.Cgy_Code;
			parameters[8].Value = model.Cgy_Name;
			parameters[9].Value = Common.Tools.IsValueNull(model.Ysy_Code);
			parameters[10].Value = Common.Tools.IsValueNull(model.Ysy_Name);
			parameters[11].Value = Common.Tools.IsValueNull(model.Ys_Date);
			parameters[12].Value = model.Dd_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Dd_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Dd1 ");
			strSql.Append(" where Dd_Code=@Dd_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Dd_Code", SqlDbType.Char,9)          };
			parameters[0].Value = Dd_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Dd_Codelist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Dd1 ");
			strSql.Append(" where Dd_Code in (" + Dd_Codelist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDd1 GetModel(string Dd_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Dd_Date,Dd_Code,Kh_Code,Kh_Name,Dd_Money,Ys_Money,Dd_Memo,Dd_Finish,Cgy_Code,Cgy_Name,Ysy_Code,Ysy_Name,Ys_Date from Dd1 ");
			strSql.Append(" where Dd_Code=@Dd_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Dd_Code", SqlDbType.Char,9)          };
			parameters[0].Value = Dd_Code;

			Model.MdlDd1 model = new Model.MdlDd1();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDd1 DataRowToModel(DataRow row)
		{
			Model.MdlDd1 model = new Model.MdlDd1();
			if (row != null)
			{
				if (row["Dd_Date"] != null && row["Dd_Date"].ToString() != "")
				{
					model.Dd_Date = DateTime.Parse(row["Dd_Date"].ToString());
				}
				if (row["Dd_Code"] != null)
				{
					model.Dd_Code = row["Dd_Code"].ToString();
				}
				if (row["Kh_Code"] != null)
				{
					model.Kh_Code = row["Kh_Code"].ToString();
				}
				if (row["Kh_Name"] != null)
				{
					model.Kh_Name = row["Kh_Name"].ToString();
				}
				if (row["Dd_Money"] != null && row["Dd_Money"].ToString() != "")
				{
					model.Dd_Money = decimal.Parse(row["Dd_Money"].ToString());
				}
				if (row["Ys_Money"] != null && row["Ys_Money"].ToString() != "")
				{
					model.Ys_Money = decimal.Parse(row["Ys_Money"].ToString());
				}
				if (row["Dd_Memo"] != null)
				{
					model.Dd_Memo = row["Dd_Memo"].ToString();
				}
				if (row["Dd_Finish"] != null)
				{
					model.Dd_Finish = row["Dd_Finish"].ToString();
				}
				if (row["Cgy_Code"] != null)
				{
					model.Cgy_Code = row["Cgy_Code"].ToString();
				}
				if (row["Cgy_Name"] != null)
				{
					model.Cgy_Name = row["Cgy_Name"].ToString();
				}
				if (row["Ysy_Code"] != null)
				{
					model.Ysy_Code = row["Ysy_Code"].ToString();
				}
				if (row["Ysy_Name"] != null)
				{
					model.Ysy_Name = row["Ysy_Name"].ToString();
				}
				if (row["Ys_Date"] != null && row["Ys_Date"].ToString() != "")
				{
					model.Ys_Date = DateTime.Parse(row["Ys_Date"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Dd_Date,Dd_Code,Kh_Code,Kh_Name,Dd_Money,Ys_Money,Dd_Memo,Dd_Finish,Cgy_Code,Cgy_Name,Ysy_Code,Ysy_Name,Ys_Date ");
			strSql.Append(" FROM Dd1 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Dd_Date,Dd_Code,Kh_Code,Kh_Name,Dd_Money,Ys_Money,Dd_Memo,Dd_Finish,Cgy_Code,Cgy_Name,Ysy_Code,Ysy_Name,Ys_Date ");
			strSql.Append(" FROM Dd1 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Dd1 ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Dd_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Dd1 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Dd1";
			parameters[1].Value = "Dd_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = DateTime.Now.ToString("yyMMdd") + Common.WinFormVar.Var.DbHelper.F_MaxCode($"SELECT MAX(RIGHT(Dd_Code,3)) FROM Dd1 WHERE LEFT(Dd_Code,6)='" + DateTime.Now.ToString("yyMMdd") + "'", length - 6);
			return max;
		}

		/// <summary>
		/// 完成订单验收
		/// </summary>
		/// <param name="ddCode">订单编码</param>
		/// <returns>是否成功</returns>
		public bool Complete(string ddCode)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Dd1 set ");
			strSql.Append("Dd_Finish=@Dd_Finish");
			strSql.Append(" where Dd_Code=@Dd_Code");
			SqlParameter[] parameters = {
				new SqlParameter("@Dd_Finish", SqlDbType.VarChar,1),
				new SqlParameter("@Dd_Code", SqlDbType.VarChar,50)
			};
			parameters[0].Value = "1";
			parameters[1].Value = ddCode;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		#endregion  ExtensionMethod
	}
}

