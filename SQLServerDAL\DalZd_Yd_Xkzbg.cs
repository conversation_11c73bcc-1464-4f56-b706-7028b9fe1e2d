﻿/**  版本信息模板在安装目录下，可自行修改。
* DalZd_Yd_Xkzbg.cs
*
* 功 能： N/A
* 类 名： DalZd_Yd_Xkzbg
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:23   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalZd_Yd_Xkzbg
	/// </summary>
	public partial class DalZd_Yd_Xkzbg : IDalZd_Yd_Xkzbg
	{
		public DalZd_Yd_Xkzbg()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Xkz_BgCode)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from Zd_Yd_Xkzbg");
			strSql.Append(" where Xkz_BgCode=@Xkz_BgCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xkz_BgCode", SqlDbType.Char,5)           };
			parameters[0].Value = Xkz_BgCode;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlZd_Yd_Xkzbg model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into Zd_Yd_Xkzbg(");
			strSql.Append("Xkz_BgCode,Xkz_BgDate,Xkz_Bgq,Xkz_Bgh,Lr_Date,Jsr_Code,Jsr_Name,Xkz_BgMemo)");
			strSql.Append(" values (");
			strSql.Append("@Xkz_BgCode,@Xkz_BgDate,@Xkz_Bgq,@Xkz_Bgh,@Lr_Date,@Jsr_Code,@Jsr_Name,@Xkz_BgMemo)");
			SqlParameter[] parameters = {
					new SqlParameter("@Xkz_BgCode", SqlDbType.Char,5),
					new SqlParameter("@Xkz_BgDate", SqlDbType.SmallDateTime),
					new SqlParameter("@Xkz_Bgq", SqlDbType.VarChar,50),
					new SqlParameter("@Xkz_Bgh", SqlDbType.VarChar,50),
					new SqlParameter("@Lr_Date", SqlDbType.DateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Jsr_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Xkz_BgMemo", SqlDbType.VarChar,200)};
			parameters[0].Value = model.Xkz_BgCode;
			parameters[1].Value = model.Xkz_BgDate;
			parameters[2].Value = model.Xkz_Bgq;
			parameters[3].Value = model.Xkz_Bgh;
			parameters[4].Value = model.Lr_Date;
			parameters[5].Value = model.Jsr_Code;
			parameters[6].Value = model.Jsr_Name;
			parameters[7].Value = model.Xkz_BgMemo;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlZd_Yd_Xkzbg model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Zd_Yd_Xkzbg set ");
			strSql.Append("Xkz_BgDate=@Xkz_BgDate,");
			strSql.Append("Xkz_Bgq=@Xkz_Bgq,");
			strSql.Append("Xkz_Bgh=@Xkz_Bgh,");
			strSql.Append("Lr_Date=@Lr_Date,");
			strSql.Append("Jsr_Code=@Jsr_Code,");
			strSql.Append("Jsr_Name=@Jsr_Name,");
			strSql.Append("Xkz_BgMemo=@Xkz_BgMemo");
			strSql.Append(" where Xkz_BgCode=@Xkz_BgCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xkz_BgDate", SqlDbType.SmallDateTime),
					new SqlParameter("@Xkz_Bgq", SqlDbType.VarChar,50),
					new SqlParameter("@Xkz_Bgh", SqlDbType.VarChar,50),
					new SqlParameter("@Lr_Date", SqlDbType.DateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Jsr_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Xkz_BgMemo", SqlDbType.VarChar,200),
					new SqlParameter("@Xkz_BgCode", SqlDbType.Char,5)};
			parameters[0].Value = model.Xkz_BgDate;
			parameters[1].Value = model.Xkz_Bgq;
			parameters[2].Value = model.Xkz_Bgh;
			parameters[3].Value = model.Lr_Date;
			parameters[4].Value = model.Jsr_Code;
			parameters[5].Value = model.Jsr_Name;
			parameters[6].Value = model.Xkz_BgMemo;
			parameters[7].Value = model.Xkz_BgCode;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Xkz_BgCode)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_Yd_Xkzbg ");
			strSql.Append(" where Xkz_BgCode=@Xkz_BgCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xkz_BgCode", SqlDbType.Char,5)           };
			parameters[0].Value = Xkz_BgCode;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Xkz_BgCodelist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_Yd_Xkzbg ");
			strSql.Append(" where Xkz_BgCode in (" + Xkz_BgCodelist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_Yd_Xkzbg GetModel(string Xkz_BgCode)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Xkz_BgCode,Xkz_BgDate,Xkz_Bgq,Xkz_Bgh,Lr_Date,Jsr_Code,Jsr_Name,Xkz_BgMemo from Zd_Yd_Xkzbg ");
			strSql.Append(" where Xkz_BgCode=@Xkz_BgCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xkz_BgCode", SqlDbType.Char,5)           };
			parameters[0].Value = Xkz_BgCode;

			Model.MdlZd_Yd_Xkzbg model = new Model.MdlZd_Yd_Xkzbg();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_Yd_Xkzbg DataRowToModel(DataRow row)
		{
			Model.MdlZd_Yd_Xkzbg model = new Model.MdlZd_Yd_Xkzbg();
			if (row != null)
			{
				if (row["Xkz_BgCode"] != null)
				{
					model.Xkz_BgCode = row["Xkz_BgCode"].ToString();
				}
				if (row["Xkz_BgDate"] != null && row["Xkz_BgDate"].ToString() != "")
				{
					model.Xkz_BgDate = DateTime.Parse(row["Xkz_BgDate"].ToString());
				}
				if (row["Xkz_Bgq"] != null)
				{
					model.Xkz_Bgq = row["Xkz_Bgq"].ToString();
				}
				if (row["Xkz_Bgh"] != null)
				{
					model.Xkz_Bgh = row["Xkz_Bgh"].ToString();
				}
				if (row["Lr_Date"] != null && row["Lr_Date"].ToString() != "")
				{
					model.Lr_Date = DateTime.Parse(row["Lr_Date"].ToString());
				}
				if (row["Jsr_Code"] != null)
				{
					model.Jsr_Code = row["Jsr_Code"].ToString();
				}
				if (row["Jsr_Name"] != null)
				{
					model.Jsr_Name = row["Jsr_Name"].ToString();
				}
				if (row["Xkz_BgMemo"] != null)
				{
					model.Xkz_BgMemo = row["Xkz_BgMemo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Xkz_BgCode,Xkz_BgDate,Xkz_Bgq,Xkz_Bgh,Lr_Date,Jsr_Code,Jsr_Name,Xkz_BgMemo ");
			strSql.Append(" FROM Zd_Yd_Xkzbg ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Xkz_BgCode,Xkz_BgDate,Xkz_Bgq,Xkz_Bgh,Lr_Date,Jsr_Code,Jsr_Name,Xkz_BgMemo ");
			strSql.Append(" FROM Zd_Yd_Xkzbg ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Zd_Yd_Xkzbg ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Xkz_BgCode desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_Yd_Xkzbg T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_Yd_Xkzbg";
			parameters[1].Value = "Xkz_BgCode";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = (string)(Common.WinFormVar.Var.DbHelper.F_MaxCode("SELECT MAX(Xkz_BgCode) FROM Zd_Yd_Xkzbg where LEN(Xkz_BgCode)=" + length, length));
			return max;
		}

		#endregion  ExtensionMethod
	}
}

