﻿/**  版本信息模板在安装目录下，可自行修改。
* DalZd_Yd_Mdzy.cs
*
* 功 能： N/A
* 类 名： DalZd_Yd_Mdzy
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:22   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalZd_Yd_Mdzy
	/// </summary>
	public partial class DalZd_Yd_Mdzy : IDalZd_Yd_Mdzy
	{
		public DalZd_Yd_Mdzy()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Mdzy_Code)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from Zd_Yd_Mdzy");
			strSql.Append(" where Mdzy_Code=@Mdzy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Mdzy_Code", SqlDbType.Char,7)            };
			parameters[0].Value = Mdzy_Code;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlZd_Yd_Mdzy model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into Zd_Yd_Mdzy(");
			strSql.Append("Mdzy_Code,Mdzy_Name,Mdzy_Sfzh,Mdzy_Xl,Mdzy_Zc,Mdzy_Gznx,Mdzy_Sgz,Mdzy_Memo)");
			strSql.Append(" values (");
			strSql.Append("@Mdzy_Code,@Mdzy_Name,@Mdzy_Sfzh,@Mdzy_Xl,@Mdzy_Zc,@Mdzy_Gznx,@Mdzy_Sgz,@Mdzy_Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@Mdzy_Code", SqlDbType.Char,7),
					new SqlParameter("@Mdzy_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Mdzy_Sfzh", SqlDbType.VarChar,50),
					new SqlParameter("@Mdzy_Xl", SqlDbType.VarChar,50),
					new SqlParameter("@Mdzy_Zc", SqlDbType.VarChar,50),
					new SqlParameter("@Mdzy_Gznx", SqlDbType.VarChar,50),
					new SqlParameter("@Mdzy_Sgz", SqlDbType.VarChar,50),
					new SqlParameter("@Mdzy_Memo", SqlDbType.VarChar,200)};
			parameters[0].Value = model.Mdzy_Code;
			parameters[1].Value = model.Mdzy_Name;
			parameters[2].Value = model.Mdzy_Sfzh;
			parameters[3].Value = model.Mdzy_Xl;
			parameters[4].Value = model.Mdzy_Zc;
			parameters[5].Value = model.Mdzy_Gznx;
			parameters[6].Value = model.Mdzy_Sgz;
			parameters[7].Value = model.Mdzy_Memo;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlZd_Yd_Mdzy model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Zd_Yd_Mdzy set ");
			strSql.Append("Mdzy_Name=@Mdzy_Name,");
			strSql.Append("Mdzy_Sfzh=@Mdzy_Sfzh,");
			strSql.Append("Mdzy_Xl=@Mdzy_Xl,");
			strSql.Append("Mdzy_Zc=@Mdzy_Zc,");
			strSql.Append("Mdzy_Gznx=@Mdzy_Gznx,");
			strSql.Append("Mdzy_Sgz=@Mdzy_Sgz,");
			strSql.Append("Mdzy_Memo=@Mdzy_Memo");
			strSql.Append(" where Mdzy_Code=@Mdzy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Mdzy_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Mdzy_Sfzh", SqlDbType.VarChar,50),
					new SqlParameter("@Mdzy_Xl", SqlDbType.VarChar,50),
					new SqlParameter("@Mdzy_Zc", SqlDbType.VarChar,50),
					new SqlParameter("@Mdzy_Gznx", SqlDbType.VarChar,50),
					new SqlParameter("@Mdzy_Sgz", SqlDbType.VarChar,50),
					new SqlParameter("@Mdzy_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Mdzy_Code", SqlDbType.Char,7)};
			parameters[0].Value = model.Mdzy_Name;
			parameters[1].Value = model.Mdzy_Sfzh;
			parameters[2].Value = model.Mdzy_Xl;
			parameters[3].Value = model.Mdzy_Zc;
			parameters[4].Value = model.Mdzy_Gznx;
			parameters[5].Value = model.Mdzy_Sgz;
			parameters[6].Value = model.Mdzy_Memo;
			parameters[7].Value = model.Mdzy_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Mdzy_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_Yd_Mdzy ");
			strSql.Append(" where Mdzy_Code=@Mdzy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Mdzy_Code", SqlDbType.Char,7)            };
			parameters[0].Value = Mdzy_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Mdzy_Codelist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_Yd_Mdzy ");
			strSql.Append(" where Mdzy_Code in (" + Mdzy_Codelist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_Yd_Mdzy GetModel(string Mdzy_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Mdzy_Code,Mdzy_Name,Mdzy_Sfzh,Mdzy_Xl,Mdzy_Zc,Mdzy_Gznx,Mdzy_Sgz,Mdzy_Memo from Zd_Yd_Mdzy ");
			strSql.Append(" where Mdzy_Code=@Mdzy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Mdzy_Code", SqlDbType.Char,7)            };
			parameters[0].Value = Mdzy_Code;

			Model.MdlZd_Yd_Mdzy model = new Model.MdlZd_Yd_Mdzy();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_Yd_Mdzy DataRowToModel(DataRow row)
		{
			Model.MdlZd_Yd_Mdzy model = new Model.MdlZd_Yd_Mdzy();
			if (row != null)
			{
				if (row["Mdzy_Code"] != null)
				{
					model.Mdzy_Code = row["Mdzy_Code"].ToString();
				}
				if (row["Mdzy_Name"] != null)
				{
					model.Mdzy_Name = row["Mdzy_Name"].ToString();
				}
				if (row["Mdzy_Sfzh"] != null)
				{
					model.Mdzy_Sfzh = row["Mdzy_Sfzh"].ToString();
				}
				if (row["Mdzy_Xl"] != null)
				{
					model.Mdzy_Xl = row["Mdzy_Xl"].ToString();
				}
				if (row["Mdzy_Zc"] != null)
				{
					model.Mdzy_Zc = row["Mdzy_Zc"].ToString();
				}
				if (row["Mdzy_Gznx"] != null)
				{
					model.Mdzy_Gznx = row["Mdzy_Gznx"].ToString();
				}
				if (row["Mdzy_Sgz"] != null)
				{
					model.Mdzy_Sgz = row["Mdzy_Sgz"].ToString();
				}
				if (row["Mdzy_Memo"] != null)
				{
					model.Mdzy_Memo = row["Mdzy_Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Mdzy_Code,Mdzy_Name,Mdzy_Sfzh,Mdzy_Xl,Mdzy_Zc,Mdzy_Gznx,Mdzy_Sgz,Mdzy_Memo ");
			strSql.Append(" FROM Zd_Yd_Mdzy ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Mdzy_Code,Mdzy_Name,Mdzy_Sfzh,Mdzy_Xl,Mdzy_Zc,Mdzy_Gznx,Mdzy_Sgz,Mdzy_Memo ");
			strSql.Append(" FROM Zd_Yd_Mdzy ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Zd_Yd_Mdzy ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Mdzy_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_Yd_Mdzy T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_Yd_Mdzy";
			parameters[1].Value = "Mdzy_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = (string)(Common.WinFormVar.Var.DbHelper.F_MaxCode("SELECT MAX(Mdzy_Code) FROM Zd_Yd_Mdzy where LEN(Mdzy_Code)=" + length, length));
			return max;
		}

		#endregion  ExtensionMethod
	}
}

