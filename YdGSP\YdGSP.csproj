﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{AD82E39B-996D-4075-932D-2E5B5A60731D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>YdGSP</RootNamespace>
    <AssemblyName>YdGSP</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=944ae1ea0e47ca04, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1List.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="不合格药品锁定\Yp_Bhg1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="不合格药品锁定\Yp_Bhg1.Designer.cs">
      <DependentUpon>Yp_Bhg1.cs</DependentUpon>
    </Compile>
    <Compile Include="不合格药品锁定\Yp_Bhg2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="不合格药品锁定\Yp_Bhg2.Designer.cs">
      <DependentUpon>Yp_Bhg2.cs</DependentUpon>
    </Compile>
    <Compile Include="不良反应记录\Zd_Blfy1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="不良反应记录\Zd_Blfy1.Designer.cs">
      <DependentUpon>Zd_Blfy1.cs</DependentUpon>
    </Compile>
    <Compile Include="不良反应记录\Zd_Blfy2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="不良反应记录\Zd_Blfy2.Designer.cs">
      <DependentUpon>Zd_Blfy2.cs</DependentUpon>
    </Compile>
    <Compile Include="人员培训计划\Zd_PxJh1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="人员培训计划\Zd_PxJh1.Designer.cs">
      <DependentUpon>Zd_PxJh1.cs</DependentUpon>
    </Compile>
    <Compile Include="人员培训计划\Zd_PxJh2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="人员培训计划\Zd_PxJh2.Designer.cs">
      <DependentUpon>Zd_PxJh2.cs</DependentUpon>
    </Compile>
    <Compile Include="人员培训计划\Zd_PxJh3.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="人员培训计划\Zd_PxJh3.Designer.cs">
      <DependentUpon>Zd_PxJh3.cs</DependentUpon>
    </Compile>
    <Compile Include="体检记录\Zd_TjJh1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="体检记录\Zd_TjJh1.Designer.cs">
      <DependentUpon>Zd_TjJh1.cs</DependentUpon>
    </Compile>
    <Compile Include="体检记录\Zd_TjJh2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="体检记录\Zd_TjJh2.Designer.cs">
      <DependentUpon>Zd_TjJh2.cs</DependentUpon>
    </Compile>
    <Compile Include="体检记录\Zd_TjJh3.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="体检记录\Zd_TjJh3.Designer.cs">
      <DependentUpon>Zd_TjJh3.cs</DependentUpon>
    </Compile>
    <Compile Include="设备检查\Zd_SbJc1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="设备检查\Zd_SbJc1.Designer.cs">
      <DependentUpon>Zd_SbJc1.cs</DependentUpon>
    </Compile>
    <Compile Include="设备检查\Zd_SbJc2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="设备检查\Zd_SbJc2.Designer.cs">
      <DependentUpon>Zd_SbJc2.cs</DependentUpon>
    </Compile>
    <Compile Include="温度湿度记录\Zd_Wsd1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="温度湿度记录\Zd_Wsd1.Designer.cs">
      <DependentUpon>Zd_Wsd1.cs</DependentUpon>
    </Compile>
    <Compile Include="温度湿度记录\Zd_Wsd2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="温度湿度记录\Zd_Wsd2.Designer.cs">
      <DependentUpon>Zd_Wsd2.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BLL\BLL.csproj">
      <Project>{46b795c2-6efa-41e6-948e-66f92e591b6a}</Project>
      <Name>BLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.BaseForm\Common.BaseForm.csproj">
      <Project>{1dd7020c-8603-438a-8015-34702dabc229}</Project>
      <Name>Common.BaseForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Delegate\Common.Delegate.csproj">
      <Project>{943ed6dc-c1fb-42fa-b543-9afaa67ba7c3}</Project>
      <Name>Common.Delegate</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Enum\Common.Enum.csproj">
      <Project>{eca72bf5-a6c2-4ecb-a80a-9723ef1098a1}</Project>
      <Name>Common.Enum</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.WinFormVar\Common.WinFormVar.csproj">
      <Project>{e267bdd2-634a-405b-bdbf-55354adbc027}</Project>
      <Name>Common.WinFormVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92e350a0-3691-4b8d-a07e-ebb0f10e6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12bf4168-d60e-4a6c-85bf-926130ee6a6d}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomSunnyUI\CustomSunnyUI.csproj">
      <Project>{e12e3d1c-d546-447d-9cd7-1ac63e8d7f18}</Project>
      <Name>CustomSunnyUI</Name>
    </ProjectReference>
    <ProjectReference Include="..\Model\MODEL.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>MODEL</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdControl\YdControl.csproj">
      <Project>{186009e9-7a04-4519-a696-79e57bb80b4e}</Project>
      <Name>YdControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdEnum\YdEnum.csproj">
      <Project>{2658ea9e-035b-43e4-b40f-6cebe092f702}</Project>
      <Name>YdEnum</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdPublicFunction\YdPublicFunction.csproj">
      <Project>{dfd1bd9d-45ff-4998-99bf-2a661d038f7c}</Project>
      <Name>YdPublicFunction</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdResources\YdResources.csproj">
      <Project>{fd356f66-2186-4b9d-b45b-e8e7b6040a8a}</Project>
      <Name>YdResources</Name>
    </ProjectReference>
    <ProjectReference Include="..\YdVar\YdVar.csproj">
      <Project>{4596a1b7-93c2-4ff7-9412-a9b49e7beb6a}</Project>
      <Name>YdVar</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="不合格药品锁定\Yp_Bhg1.resx">
      <DependentUpon>Yp_Bhg1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="不合格药品锁定\Yp_Bhg2.resx">
      <DependentUpon>Yp_Bhg2.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="不良反应记录\Zd_Blfy1.resx">
      <DependentUpon>Zd_Blfy1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="不良反应记录\Zd_Blfy2.resx">
      <DependentUpon>Zd_Blfy2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="人员培训计划\Zd_PxJh1.resx">
      <DependentUpon>Zd_PxJh1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="人员培训计划\Zd_PxJh2.resx">
      <DependentUpon>Zd_PxJh2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="人员培训计划\Zd_PxJh3.resx">
      <DependentUpon>Zd_PxJh3.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="体检记录\Zd_TjJh1.resx">
      <DependentUpon>Zd_TjJh1.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="体检记录\Zd_TjJh2.resx">
      <DependentUpon>Zd_TjJh2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="体检记录\Zd_TjJh3.resx">
      <DependentUpon>Zd_TjJh3.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="设备检查\Zd_SbJc1.resx">
      <DependentUpon>Zd_SbJc1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="设备检查\Zd_SbJc2.resx">
      <DependentUpon>Zd_SbJc2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="温度湿度记录\Zd_Wsd1.resx">
      <DependentUpon>Zd_Wsd1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="温度湿度记录\Zd_Wsd2.resx">
      <DependentUpon>Zd_Wsd2.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\增加.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\查询.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\删除.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>