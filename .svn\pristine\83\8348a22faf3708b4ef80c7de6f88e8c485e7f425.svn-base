namespace YdGSP
{
    partial class Yp_Bhg1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Yp_Bhg1));
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.C1ToolBar1 = new C1.Win.C1Command.C1ToolBar();
            this.C1CommandLink1 = new C1.Win.C1Command.C1CommandLink();
            this.CmdAdd = new C1.Win.C1Command.C1Command();
            this.C1CommandLink2 = new C1.Win.C1Command.C1CommandLink();
            this.CmdDelete = new C1.Win.C1Command.C1Command();
            this.C1CommandLink3 = new C1.Win.C1Command.C1CommandLink();
            this.CmdRefresh = new C1.Win.C1Command.C1Command();
            this.LblTotal = new System.Windows.Forms.Label();
            this.TxtFilter = new CustomControl.MyTextBox();
            this.myGrid1 = new CustomControl.MyGrid();
            this.c1CommandHolder1 = new C1.Win.C1Command.C1CommandHolder();
            this.tableLayoutPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).BeginInit();
            this.SuspendLayout();
            // 
            // imageList1
            // 
            this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
            this.imageList1.Images.SetKeyName(0, "增加.png");
            this.imageList1.Images.SetKeyName(1, "删除.png");
            this.imageList1.Images.SetKeyName(2, "打印.png");
            this.imageList1.Images.SetKeyName(3, "导入.png");
            this.imageList1.Images.SetKeyName(4, "导出.png");
            this.imageList1.Images.SetKeyName(5, "上移.png");
            this.imageList1.Images.SetKeyName(6, "下移.png");
            this.imageList1.Images.SetKeyName(7, "启用.png");
            this.imageList1.Images.SetKeyName(8, "停用.png");
            this.imageList1.Images.SetKeyName(9, "刷新.png");
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 3;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.Controls.Add(this.C1ToolBar1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.LblTotal, 2, 0);
            this.tableLayoutPanel1.Controls.Add(this.TxtFilter, 1, 0);
            this.tableLayoutPanel1.Controls.Add(this.myGrid1, 0, 1);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 2;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(1200, 600);
            this.tableLayoutPanel1.TabIndex = 6;
            // 
            // C1ToolBar1
            // 
            this.C1ToolBar1.AccessibleName = "Tool Bar";
            this.C1ToolBar1.BackColor = System.Drawing.Color.Transparent;
            this.C1ToolBar1.ButtonLayoutHorz = C1.Win.C1Command.ButtonLayoutEnum.TextBelow;
            this.C1ToolBar1.CommandHolder = null;
            this.C1ToolBar1.CommandLinks.AddRange(new C1.Win.C1Command.C1CommandLink[] {
            this.C1CommandLink1,
            this.C1CommandLink2,
            this.C1CommandLink3});
            this.C1ToolBar1.Location = new System.Drawing.Point(3, 3);
            this.C1ToolBar1.Name = "C1ToolBar1";
            this.C1ToolBar1.Size = new System.Drawing.Size(117, 44);
            this.C1ToolBar1.Text = "C1ToolBar1";
            this.C1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom;
            this.C1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic;
            // 
            // C1CommandLink1
            // 
            this.C1CommandLink1.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink1.Command = this.CmdAdd;
            // 
            // CmdAdd
            // 
            this.CmdAdd.ImageIndex = 0;
            this.CmdAdd.Name = "CmdAdd";
            this.CmdAdd.ShortcutText = "";
            this.CmdAdd.Text = "新增";
            this.CmdAdd.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdAdd_Click);
            // 
            // C1CommandLink2
            // 
            this.C1CommandLink2.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink2.Command = this.CmdDelete;
            this.C1CommandLink2.SortOrder = 1;
            // 
            // CmdDelete
            // 
            this.CmdDelete.ImageIndex = 1;
            this.CmdDelete.Name = "CmdDelete";
            this.CmdDelete.ShortcutText = "";
            this.CmdDelete.Text = "删除";
            this.CmdDelete.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdDelete_Click);
            // 
            // C1CommandLink3
            // 
            this.C1CommandLink3.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink3.Command = this.CmdRefresh;
            this.C1CommandLink3.SortOrder = 2;
            // 
            // CmdRefresh
            // 
            this.CmdRefresh.ImageIndex = 9;
            this.CmdRefresh.Name = "CmdRefresh";
            this.CmdRefresh.ShortcutText = "";
            this.CmdRefresh.Text = "刷新";
            this.CmdRefresh.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdRefresh_Click);
            // 
            // LblTotal
            // 
            this.LblTotal.Anchor = System.Windows.Forms.AnchorStyles.Left;
            this.LblTotal.AutoSize = true;
            this.LblTotal.Location = new System.Drawing.Point(551, 21);
            this.LblTotal.Margin = new System.Windows.Forms.Padding(3, 6, 3, 0);
            this.LblTotal.Name = "LblTotal";
            this.LblTotal.Size = new System.Drawing.Size(35, 14);
            this.LblTotal.TabIndex = 2;
            this.LblTotal.Text = "∑=0";
            // 
            // TxtFilter
            // 
            this.TxtFilter.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtFilter.Captain = "过滤框";
            this.TxtFilter.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtFilter.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtFilter.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtFilter.CaptainWidth = 55F;
            this.TxtFilter.ContentForeColor = System.Drawing.Color.Black;
            this.TxtFilter.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtFilter.EditMask = null;
            this.TxtFilter.Location = new System.Drawing.Point(126, 13);
            this.TxtFilter.Multiline = false;
            this.TxtFilter.Name = "TxtFilter";
            this.TxtFilter.PasswordChar = '\0';
            this.TxtFilter.ReadOnly = false;
            this.TxtFilter.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtFilter.SelectionStart = 0;
            this.TxtFilter.SelectStart = 0;
            this.TxtFilter.Size = new System.Drawing.Size(419, 23);
            this.TxtFilter.TabIndex = 1;
            this.TxtFilter.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtFilter.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtFilter.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtFilter.Watermark = "输入不合格编码、药品名称、简称、生产批号、批准文号进行筛选";
            this.TxtFilter.TextChanged += new System.EventHandler(this.TxtFilter_TextChanged);
            // 
            // myGrid1
            // 
            this.myGrid1.AllowColMove = true;
            this.myGrid1.AllowFilter = true;
            this.myGrid1.CanCustomCol = false;
            this.myGrid1.Caption = "";
            this.myGrid1.ChildGrid = null;
            this.myGrid1.Col = 0;
            this.myGrid1.ColumnFooters = false;
            this.myGrid1.ColumnHeaders = true;
            this.tableLayoutPanel1.SetColumnSpan(this.myGrid1, 3);
            this.myGrid1.DataMember = "";
            this.myGrid1.DataSource = null;
            this.myGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid1.FetchRowStyles = false;
            this.myGrid1.FilterBar = false;
            this.myGrid1.GroupByAreaVisible = true;
            this.myGrid1.Location = new System.Drawing.Point(0, 50);
            this.myGrid1.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid1.Name = "myGrid1";
            this.myGrid1.Size = new System.Drawing.Size(1200, 550);
            this.myGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid1.TabIndex = 3;
            this.myGrid1.Xmlpath = null;
            // 
            // c1CommandHolder1
            // 
            this.c1CommandHolder1.Commands.Add(this.CmdAdd);
            this.c1CommandHolder1.Commands.Add(this.CmdDelete);
            this.c1CommandHolder1.Commands.Add(this.CmdRefresh);
            this.c1CommandHolder1.ImageList = this.imageList1;
            this.c1CommandHolder1.Owner = this;
            // 
            // Yp_Bhg1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1200, 600);
            this.Controls.Add(this.tableLayoutPanel1);
            this.Name = "Yp_Bhg1";
            this.Text = "不合格药品锁定";
            this.Load += new System.EventHandler(this.Yp_Bhg1_Load);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private C1.Win.C1Command.C1ToolBar C1ToolBar1;
        private C1.Win.C1Command.C1CommandLink C1CommandLink1;
        private C1.Win.C1Command.C1Command CmdAdd;
        private C1.Win.C1Command.C1CommandLink C1CommandLink2;
        private C1.Win.C1Command.C1Command CmdDelete;
        private C1.Win.C1Command.C1CommandLink C1CommandLink3;
        private C1.Win.C1Command.C1Command CmdRefresh;
        private System.Windows.Forms.Label LblTotal;
        private CustomControl.MyTextBox TxtFilter;
        private CustomControl.MyGrid myGrid1;
        private C1.Win.C1Command.C1CommandHolder c1CommandHolder1;
    }
}
